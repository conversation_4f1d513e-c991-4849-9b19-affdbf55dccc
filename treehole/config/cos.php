<?php

// 检查是否为本地环境
$host = $_SERVER['HTTP_HOST'] ?? '';
$isLocal = strpos($host, 'localhost') !== false ||
           strpos($host, '127.0.0.1') !== false ||
           strpos($host, '192.168.') !== false;

return [
    // 基础配置
    'region' => 'ap-beijing',
    'credentials' => [
        'secretId' => $isLocal ? 'AKIDhGbllaEhx54qRpdIjy0oOo6FF3f3D9rM' : 'AKIDhGbllaEhx54qRpdIjy0oOo6FF3f3D9rM',
        'secretKey' => $isLocal ? 'KTQCl6UgQzZ1GRSHxRhnhOs8B2g6II9p' : 'KTQCl6UgQzZ1GRSHxRhnhOs8B2g6II9p',
    ],

    // 存储桶配置
    'buckets' => [
        // 公有存储桶（普通图片）
        'public' => [
            'bucket' => 'treeholepublic-1320255796',  // 你的公有存储桶名称
            'domains' => [
                'default' => 'https://treeholepublic-1320255796.cos.ap-beijing.myqcloud.com', // 开发环境直连COS
                'production' => 'https://public.bjgaoxiaoshequ.store', // EdgeOne加速域名
            ],
            'acl' => 'public-read',
            'acceleration' => 'edgeone' // 标记使用EdgeOne加速
        ],

        // 私有存储桶（敏感文件）
        'private' => [
            'bucket' => 'treehole-1320255796', // 你的私有存储桶名称
            'domains' => [
                'default' => 'https://treehole-1320255796.cos.ap-beijing.myqcloud.com', // 开发环境直连COS
                'production' => 'https://private.bjgaoxiaoshequ.store', // EdgeOne加速域名
            ],
            'acl' => 'private',
            'acceleration' => 'edgeone' // 标记使用EdgeOne加速
        ]
    ],

    // 文件类型映射
    'file_mapping' => [
        // 公有存储桶文件类型
        'public_types' => ['comment', 'life', 'group', 'canteen', 'activity', 'liaoran', 'common', 'avatar', 'anonymous-avatar'], // 添加anonymous-avatar到公有存储桶
        // 私有存储桶文件类型
        'private_types' => ['auth', 'schedule', 'touxiang', 'renzheng', 'kebiao'] // 移除avatar
    ],

    // 本地开发配置
    'local_config' => [
        'enable_cos' => true, // 本地启用COS
        'fallback_to_local' => false, // 不回退到本地存储，强制使用COS
    ]
];
