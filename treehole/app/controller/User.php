<?php
namespace app\controller;

use app\BaseController;
use app\util\SecretUtil;
use app\service\system\CacheService;
use think\facade\Db;
use think\Request;
use think\response\Json;
use app\util\JwtUtil;
use app\util\ImageSecurityUtil;
use app\util\CosUtil;

class User extends BaseController
{
    public function wxLogin(Request $request): Json
    {
        $code = $request->post('code');
        if (!$code) {
            return json(['error_code' => 1, 'msg' => '缺少code']);
        }

        // 获取微信小程序配置（使用缓存）
        $cacheKey = 'wechat_config_main';
        $wechatConfig = \app\service\system\CacheService::get($cacheKey);
        if (!$wechatConfig) {
            $wechatConfig = SecretUtil::getWechatMiniprogram('main');
            \app\service\system\CacheService::set($cacheKey, $wechatConfig, 3600); // 缓存1小时
        }
        $appid = $wechatConfig['appid'];
        $appsecret = $wechatConfig['secret'];

        $url = "https://api.weixin.qq.com/sns/jscode2session?appid={$appid}&secret={$appsecret}&js_code={$code}&grant_type=authorization_code";

        // 使用cURL优化网络请求
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 5); // 5秒超时
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 3); // 3秒连接超时
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        if ($response === false || $httpCode !== 200) {
            return json(['error_code' => 2, 'msg' => '微信服务请求失败']);
        }

        $result = json_decode($response, true);

        if (isset($result['errcode'])) {
            return json(['error_code' => 2, 'msg' => $result['errmsg']]);
        }

        $openid = $result['openid'];
        $unionid = $result['unionid'] ?? null; // 获取unionid（如果小程序和公众号已绑定到开放平台）

        // 如果有unionid，直接保存映射关系
        if ($unionid) {
            $this->saveUserMapping($openid, $unionid);
        }

        $user = Db::name('user')
            ->field('id, username, phone, face_url, openid, titlename, titlecolor, subject_image, status, school_id, verified_university_id')
            ->where('openid', $openid)
            ->find();

        // 如果用户有学校ID，获取学校详细信息
        if ($user && $user['school_id']) {
            \think\facade\Log::info('用户有学校ID: ' . $user['school_id']);
            $schoolInfo = Db::table('beijing_universities')
                ->field('id, district, full_name, short_name, university_short_name, university_name, logo_url')
                ->where('id', $user['school_id'])
                ->find();

            if ($schoolInfo) {
                $user['school_info'] = $schoolInfo;
                \think\facade\Log::info('获取到学校信息: ' . json_encode($schoolInfo));
            } else {
                \think\facade\Log::warning('未找到学校信息，school_id: ' . $user['school_id']);
            }
        } else {
            \think\facade\Log::info('用户没有学校ID或用户不存在');
        }

        // 如果用户有认证学校ID，获取认证学校详细信息
        if ($user && $user['verified_university_id']) {
            \think\facade\Log::info('用户有认证学校ID: ' . $user['verified_university_id']);
            $verifiedSchoolInfo = Db::table('beijing_universities')
                ->field('id, district, full_name, short_name, university_short_name, university_name, logo_url, university_id')
                ->where('university_id', $user['verified_university_id'])
                ->find();

            if ($verifiedSchoolInfo) {
                $user['verified_school_info'] = $verifiedSchoolInfo;
                \think\facade\Log::info('获取到认证学校信息: ' . json_encode($verifiedSchoolInfo));
            } else {
                \think\facade\Log::warning('未找到认证学校信息，verified_university_id: ' . $user['verified_university_id']);
            }
        }

        // 生成token的数据
        $tokenData = [
            'openid' => $openid
        ];

        if ($user) {
            $tokenData['user_id'] = $user['id'];
            // 生成token
            $tokens = JwtUtil::generateToken($tokenData);

            // 处理头像URL - 头像现在存储在公有存储桶，无需签名
            if (!empty($user['face_url']) && !str_starts_with($user['face_url'], 'http')) {
                $user['face_url'] = CosUtil::generateUrl($user['face_url'], 'public');
            }

            // 保持原有返回格式，添加token字段
            $responseData = $user;
            $responseData['access_token'] = $tokens['access_token'];
            $responseData['refresh_token'] = $tokens['refresh_token'];
            $responseData['expire_in'] = $tokens['expire_in'];

            // 简化日志记录
            \think\facade\Log::info('用户登录成功', ['user_id' => $user['id'], 'school_id' => $user['school_id'] ?? null]);

            return json(['error_code' => 0, 'msg' => '登录成功', 'data' => $responseData]);
        } else {
            $randomPhone = $this->generateUniquePhone();
            $randomAvatar = rand(1, 91);
            $data = [
                'openid' => $openid,
                'username' => '航友' . $randomPhone,
                'phone' => $randomPhone,
                'face_url' => 'anonymous-avatar/touxiang' . $randomAvatar . '.png',
            ];

            $userId = Db::name('user')->insertGetId($data);
            if ($userId) {
                $this->addDefaultTouxian($openid);
                $tokenData['user_id'] = $userId;
                // 生成token
                $tokens = JwtUtil::generateToken($tokenData);
                
                // 直接使用插入的数据构建用户信息，避免重复查询
                $user = array_merge($data, [
                    'id' => $userId,
                    'titlename' => null,
                    'titlecolor' => null,
                    'subject_image' => null,
                    'status' => null,
                    'school_id' => null,
                    'verified_university_id' => null
                ]);

                // 处理头像URL - 头像现在存储在公有存储桶，无需签名
                if (!empty($user['face_url']) && !str_starts_with($user['face_url'], 'http')) {
                    $user['face_url'] = CosUtil::generateUrl($user['face_url'], 'public');
                }

                // 新注册用户无需查询学校信息（school_id和verified_university_id都为null）

                // 添加token信息
                $responseData = array_merge($user, [
                    'access_token' => $tokens['access_token'],
                    'refresh_token' => $tokens['refresh_token'],
                    'expire_in' => $tokens['expire_in']
                ]);
                
                return json(['error_code' => 0, 'msg' => '注册并登录成功', 'data' => $responseData]);
            } else {
                return json(['error_code' => 3, 'msg' => '注册失败']);
            }
        }
    }

    private function generateUniquePhone(): string
    {
        do {
            $randomPhone = $this->generateRandomString(10);
            $exists = Db::name('user')->where('phone', $randomPhone)->find();
        } while ($exists);

        return $randomPhone;
    }

    private function generateRandomString($length = 10): string
    {
        $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $randomString = '';
        for ($i = 0; $i < $length; $i++) {
            $randomString .= $characters[rand(0, strlen($characters) - 1)];
        }
        return $randomString;
    }

    private function addDefaultTouxian($openid): void
    {
        $defaultTitles = [
            ['touxian_id' => 1, 'touxian_name' => '北航小子'],
            ['touxian_id' => 2, 'touxian_name' => '学航小子'],
            ['touxian_id' => 3, 'touxian_name' => '沙航小子'],
            ['touxian_id' => 5, 'touxian_name' => '航小萱'],
            ['touxian_id' => 6, 'touxian_name' => '学小萱'],
            ['touxian_id' => 7, 'touxian_name' => '沙小萱']
        ];

        // 批量插入优化性能
        $insertData = [];
        $currentTime = time();
        foreach ($defaultTitles as $title) {
            $insertData[] = [
                'touxian_id' => $title['touxian_id'],
                'touxian_name' => $title['touxian_name'],
                'openid' => $openid,
                'create_time' => $currentTime
            ];
        }

        Db::name('touxian')->insertAll($insertData);
    }

    public function updateFaceUrlByPhone()
    {
        if (empty($_POST['phone'])) {
            return json(['error_code' => 1, 'msg' => '请登录后上传']);
        }

        if (!empty($_FILES['face']['name'])) {
            $phone = $_POST['phone'];

            // 严格的文件安全验证
            $securityCheck = ImageSecurityUtil::validateNativeFile($_FILES['face']);
            if (!$securityCheck['success']) {
                return json(['error_code' => 6, 'msg' => $securityCheck['message']]);
            }

            // 获取安全的文件扩展名
            $safeExtension = ImageSecurityUtil::getSafeExtension($securityCheck['mime_type']);
            $newFileName = $phone . '-' . time() . '.' . $safeExtension;

            // 添加调试日志
            \think\facade\Log::info('开始上传头像到COS', [
                'phone' => $phone,
                'fileName' => $newFileName,
                'fileType' => 'avatar'
            ]);

            // 上传到COS
            $result = CosUtil::uploadFile('avatar', $_FILES['face']['tmp_name'], $newFileName);

            \think\facade\Log::info('COS上传结果', $result);

            if (!$result['success']) {
                \think\facade\Log::error('COS上传失败', [
                    'error' => $result['message'],
                    'phone' => $phone,
                    'fileName' => $newFileName
                ]);
                return json(['error_code' => 4, 'msg' => '文件上传失败：' . $result['message']]);
            }

            // 存储相对路径到数据库，生成完整URL返回给前端
            $relativePath = $result['key'];  // 相对路径用于数据库存储
            $fullUrl = CosUtil::generateUrl($result['key'], $result['bucket_type']);  // 完整URL用于前端显示

            \think\facade\Log::info('生成的头像URL', [
                'key' => $result['key'],
                'bucket_type' => $result['bucket_type'],
                'relative_path' => $relativePath,
                'full_url' => $fullUrl
            ]);

            // 开启事务
            Db::startTrans();

            try {
                // 获取用户ID
                $user_id = Db::name('user')->where('phone', $phone)->value('id');

                // 更新 User 表，存储相对路径，同时增加status_code
                Db::name('user')->where('phone', $phone)->update([
                    'face_url' => $relativePath,  // 存储相对路径
                    'status_code' => Db::raw('status_code + 1')
                ]);

                // 更新 Message 表（只更新choose小于100的记录），存储相对路径
                Db::name('message')->where('user_id', $user_id)->where('choose', '<', 100)->update(['face_url' => $relativePath]);

                // 更新 Comment 表，存储相对路径
                Db::name('comment')->where('user_id', $user_id)->update(['face_url' => $relativePath]);

                // 更新 Post 表，存储相对路径
                Db::name('post')->where('user_id', $user_id)->update(['face_url' => $relativePath]);

                // 提交事务
                Db::commit();
                return json(['error_code' => 0, 'face_url' => $fullUrl]);  // 返回完整URL给前端
            } catch (\Exception $e) {
                Db::rollback();
                return json(['error_code' => 3, 'msg' => '发生错误: ' . $e->getMessage()]);
            }
        } else {
            return json(['error_code' => 5, 'msg' => '未接收到文件']);
        }
    }


    public function updateUserinformById(Request $request): Json
    {
        $userId = $request->post('user_id');
        $username = $request->post('username');
        $titlename = $request->post('titlename');
        $titlecolor = $request->post('titlecolor');

        // 参数验证
        $validate = new \app\validate\UserValidate();
        $data = [
            'user_id' => $userId,
            'username' => $username,
            'titlename' => $titlename,
            'titlecolor' => $titlecolor
        ];

        if (!$validate->check($data)) {
            \think\facade\Log::warning('用户信息更新验证失败', [
                'data' => $data,
                'error' => $validate->getError()
            ]);
            return json(['error_code' => 1, 'msg' => $validate->getError()]);
        }
        try {
            // 使用事务更新多个表
            Db::transaction(function () use ($userId, $username, $titlename, $titlecolor) {
                // 更新 user 表
                Db::name('user')->where('id', $userId)->update([
                    'username' => $username,
                    'titlename' => $titlename,
                    'titlecolor' => $titlecolor,
                    'status_code' => Db::raw('status_code + 1')
                ]);

                // 更新 message 表
                Db::name('message')->where('user_id', $userId)->where('choose', '<', 100)->update([
                    'username' => $username,
                    'titlename' => $titlename,
                    'titlecolor' => $titlecolor
                ]);

                // 更新 comment 表
                Db::name('comment')->where('user_id', $userId)->update([
                    'username' => $username,
                    'titlename' => $titlename,
                    'titlecolor' => $titlecolor
                ]);

                // 更新 post 表
                Db::name('post')->where('user_id', $userId)->update([
                    'username' => $username,
                    'titlename' => $titlename,
                    'titlecolor' => $titlecolor
                ]);
            });

            // 成功时返回
            return json(['error_code' => 0, 'msg' => '信息更新成功']);
        } catch (\Exception $e) {
            // 捕获异常并返回错误信息
            return json(['error_code' => 3, 'msg' => '更新失败: ' . $e->getMessage()]);
        }
    }

    public function unread(Request $request): Json
    {
        $userId = $request->post('user_id');
        if (!$userId) {
            return json(['status' => 0, 'msg' => 'user_id 不能为空']);
        }

        $unread = Db::name('user')->where('id', $userId)->value('unread');
        if ($unread !== null) {
            return json(['status' => 1, 'unread' => $unread, 'msg' => '查询成功']);
        } else {
            return json(['status' => 0, 'msg' => '未找到对应的用户']);
        }
    }

    public function clearUnreadMessages(Request $request): Json
    {
        $userId = $request->post('user_id');
        if (!$userId) {
            return json(['status' => 0, 'msg' => 'user_id 不能为空']);
        }

        $user = Db::name('user')->where('id', $userId)->find();
        if ($user) {
            Db::name('user')->where('id', $userId)->update(['unread' => 0]);
            return json(['status' => 1, 'unread' => 0, 'msg' => '未读消息已清零']);
        } else {
            return json(['status' => 0, 'msg' => '未找到对应的用户']);
        }
    }

    public function uploadImage(Request $request): Json
    {
        $file = $request->file('file');
        $newName = $request->post('newName');
        $id = $request->post('id');

        if (!$file || !$newName || !$id) {
            return json(['success' => false, 'msg' => '参数不完整或无效']);
        }

        // 严格的文件安全验证
        $securityCheck = ImageSecurityUtil::validateThinkPHPFile($file);
        if (!$securityCheck['success']) {
            return json(['success' => false, 'msg' => $securityCheck['message']]);
        }

        $uploadDir = 'C:/daima/treehole/public/renzheng/';

        // 验证文件名安全性 - 检查是否包含危险字符
        $dangerousChars = ['..', '/', '\\', '<', '>', ':', '"', '|', '?', '*', "\0"];
        foreach ($dangerousChars as $char) {
            if (strpos($newName, $char) !== false) {
                return json(['success' => false, 'msg' => '文件名包含非法字符']);
            }
        }

        // 确保文件扩展名与实际文件类型匹配
        $safeExtension = ImageSecurityUtil::getSafeExtension($securityCheck['mime_type']);
        $pathInfo = pathinfo($newName);
        $baseName = $pathInfo['filename'];
        $safeName = $baseName . '.' . $safeExtension;

        $uploadPath = $uploadDir . $safeName;

        // 删除旧图片
        $oldFiles = glob($uploadDir . $id . '_*.png');
        foreach ($oldFiles as $oldFile) {
            if (file_exists($oldFile)) {
                unlink($oldFile);
            }
        }

        if (move_uploaded_file($file->getRealPath(), $uploadPath)) {
            return json(['success' => true, 'newImagePath' => $uploadPath]);
        } else {
            return json(['success' => false, 'msg' => '文件上传失败']);
        }
    }

    public function getUploadedImages(Request $request): Json
    {
        $phone = $request->post('phone');
        if (empty($phone)) {
            return json(['error_code' => 1, 'msg' => '缺少必要的参数']);
        }

        // 查询用户的已上传图片URL
        $imageUrl = Db::name('user')->where('phone', $phone)->value('subject_image');

        if ($imageUrl) {
            // 返回图片的URL
            return json(['error_code' => 0, 'imageUrls' => $imageUrl]);
        } else {
            return json(['error_code' => 2, 'msg' => '没有找到图片']);
        }
    }

    /**
     * 刷新token
     */
    public function refreshToken(Request $request): Json
    {
        $refreshToken = $request->post('refresh_token');
        if (!$refreshToken) {
            return json(['error_code' => 1, 'msg' => '缺少refresh_token']);
        }

        $tokens = JwtUtil::refreshToken($refreshToken);
        if ($tokens === false) {
            return json(['error_code' => 2, 'msg' => 'refresh_token无效或已过期']);
        }

        return json([
            'error_code' => 0,
            'msg' => 'token刷新成功',
            'data' => $tokens
        ]);
    }

    /**
     * 验证用户状态
     * @param Request $request
     * @return Json
     */
    public function verifyStatus(Request $request): Json
    {
        // 获取参数
        $userId = $request->post('user_id');
        $accessToken = $request->post('access_token');

        // 验证参数
        if (!$userId || !$accessToken) {
            return json(['error_code' => 1, 'msg' => '缺少必要参数']);
        }

        try {
            // 验证token
            $decoded = JwtUtil::validateToken($accessToken);

            // 验证token中的user_id是否匹配
            if ($decoded['user_id'] != $userId) {
                return json(['error_code' => 2, 'msg' => '无效的凭证']);
            }

            // 查询用户当前状态
            $user = Db::name('user')->where('id', $userId)->find();

            if (!$user) {
                return json(['error_code' => 3, 'msg' => '用户不存在']);
            }

            // 返回用户当前状态和状态码
            return json([
                'error_code' => 0,
                'msg' => '验证成功',
                'data' => [
                    'status' => $user['status'],
                    'status_code' => $user['status_code'] ?? 1 // 如果状态码字段不存在，返回默认值1
                ]
            ]);

        } catch (\Exception $e) {
            return json(['error_code' => 4, 'msg' => '验证失败: ' . $e->getMessage()]);
        }
    }



    /**
     * 保存用户映射关系
     * @param string $openid 小程序openid
     * @param string $unionid unionid
     * @param string|null $officialOpenid 公众号openid（可选）
     */
    private function saveUserMapping($openid, $unionid, $officialOpenid = null)
    {
        try {
            // 检查是否已存在映射关系
            $existingMapping = Db::name('user_mapping')
                ->where('miniprogram_openid', $openid)
                ->find();

            if ($existingMapping) {
                // 更新现有映射关系
                $updateData = [
                    'unionid' => $unionid,
                    'updated_at' => date('Y-m-d H:i:s')
                ];

                if ($officialOpenid) {
                    $updateData['official_openid'] = $officialOpenid;
                }

                Db::name('user_mapping')
                    ->where('miniprogram_openid', $openid)
                    ->update($updateData);
            } else {
                // 创建新的映射关系
                $insertData = [
                    'miniprogram_openid' => $openid,
                    'unionid' => $unionid,
                    'created_at' => date('Y-m-d H:i:s')
                ];

                if ($officialOpenid) {
                    $insertData['official_openid'] = $officialOpenid;
                }

                Db::name('user_mapping')->insert($insertData);
            }

        } catch (\Exception $e) {
            // 静默处理异常，避免影响主要登录流程
        }
    }



    /**
     * 更新用户状态码（强制用户重新登录）
     * 需要管理员权限
     * @param Request $request
     * @return Json
     */
    public function updateUserStatus(Request $request): Json
    {
        // 获取参数
        $adminId = $request->post('admin_id');
        $adminToken = $request->post('admin_token');
        $userId = $request->post('user_id');
        $userAll = $request->post('all_users', 0); // 0表示更新单个用户，1表示更新所有用户

        // 验证参数
        if (!$adminId || !$adminToken) {
            return json(['error_code' => 1, 'msg' => '缺少管理员凭证']);
        }

        if (!$userId && !$userAll) {
            return json(['error_code' => 2, 'msg' => '请指定要更新的用户']);
        }

        try {
            // 验证管理员token
            $decoded = JwtUtil::validateToken($adminToken);
            
            // 验证token中的user_id是否匹配
            if ($decoded['user_id'] != $adminId) {
                return json(['error_code' => 3, 'msg' => '无效的管理员凭证']);
            }
            
            // 验证是否为管理员
            $admin = Db::name('user')->where('id', $adminId)->find();
            if (!$admin || $admin['status'] !== '管理员') {
                return json(['error_code' => 4, 'msg' => '非管理员，无权操作']);
            }
            
            if ($userAll == 1) {
                // 更新所有用户的状态码（递增1）
                Db::name('user')->where('id', '<>', $adminId)->update([
                    'status_code' => Db::raw('status_code + 1')
                ]);
                
                return json([
                    'error_code' => 0, 
                    'msg' => '已更新所有用户状态码'
                ]);
            } else {
                // 查询用户当前状态
                $user = Db::name('user')->where('id', $userId)->find();
                
                if (!$user) {
                    return json(['error_code' => 5, 'msg' => '用户不存在']);
                }
                
                // 更新用户状态码（递增1）
                $newStatusCode = $user['status_code'] + 1;
                Db::name('user')->where('id', $userId)->update(['status_code' => $newStatusCode]);
                
                return json([
                    'error_code' => 0, 
                    'msg' => '用户状态码已更新', 
                    'data' => [
                        'user_id' => $userId,
                        'old_status_code' => $user['status_code'],
                        'new_status_code' => $newStatusCode
                    ]
                ]);
            }
        } catch (\Exception $e) {
            return json(['error_code' => 6, 'msg' => '操作失败: ' . $e->getMessage()]);
        }
    }

    /**
     * 保存公众号提示状态
     * @return \think\Response
     */
    public function saveOfficialAccountTipStatus()
    {
        $user_id = input('post.user_id', '', 'trim');
        $show_tip = input('post.show_tip', '1', 'trim');
        
        if (empty($user_id)) {
            return json(['code' => 400, 'message' => '参数错误']);
        }
        
        // 检查用户是否存在
        $user = Db::name('user')->where('id', $user_id)->find();
        if (!$user) {
            return json(['code' => 404, 'message' => '用户不存在']);
        }
        
        // 查询是否存在记录
        $setting = Db::name('user_setting')->where('user_id', $user_id)->find();
        
        if ($setting) {
            // 存在则更新
            Db::name('user_setting')->where('user_id', $user_id)
                ->update([
                    'show_official_account_tip' => $show_tip,
                    'update_time' => time()
                ]);
        } else {
            // 不存在则创建
            Db::name('user_setting')->insert([
                'user_id' => $user_id,
                'show_official_account_tip' => $show_tip,
                'create_time' => time(),
                'update_time' => time()
            ]);
        }
        
        return json([
            'code' => 200, 
            'message' => '保存成功', 
            'data' => ['show_tip' => $show_tip]
        ]);
    }
    
    /**
     * 获取公众号提示状态
     * @return \think\Response
     */
    public function getOfficialAccountTipStatus()
    {
        $user_id = input('post.user_id', '', 'trim');
        
        if (empty($user_id)) {
            return json(['code' => 400, 'message' => '参数错误']);
        }
        
        // 获取设置
        $setting = Db::name('user_setting')->where('user_id', $user_id)->find();
        
        // 默认显示
        $show_tip = '1';
        
        if ($setting && isset($setting['show_official_account_tip'])) {
            $show_tip = $setting['show_official_account_tip'];
        }
        
        return json([
            'code' => 200,
            'message' => '获取成功',
            'data' => ['show_tip' => $show_tip]
        ]);
    }

    /**
     * 更新用户学校信息
     */
    public function updateSchool(Request $request): Json
    {
        // 从请求头获取token并验证
        $token = $request->header('token');
        if (!$token) {
            return json(['error_code' => 1, 'msg' => '缺少token']);
        }

        $userData = JwtUtil::validateToken($token);
        if (!$userData) {
            return json(['error_code' => 2, 'msg' => 'token无效或已过期']);
        }

        $userId = $userData['user_id'] ?? 0;
        $schoolId = $request->post('school_id');

        if (!$userId) {
            return json(['error_code' => 3, 'msg' => '用户ID无效']);
        }

        if (!$schoolId) {
            return json(['error_code' => 4, 'msg' => '缺少学校ID']);
        }

        try {
            // 验证学校是否存在
            $school = Db::table('beijing_universities')
                ->where('id', $schoolId)
                ->find();

            if (!$school) {
                return json(['error_code' => 5, 'msg' => '学校不存在']);
            }

            // 更新用户学校信息
            $result = Db::name('user')
                ->where('id', $userId)
                ->update(['school_id' => $schoolId]);

            if ($result !== false) {
                return json(['error_code' => 0, 'msg' => '学校信息更新成功']);
            } else {
                return json(['error_code' => 6, 'msg' => '更新失败']);
            }

        } catch (\Exception $e) {
            return json(['error_code' => 7, 'msg' => '服务器错误: ' . $e->getMessage()]);
        }
    }

    /**
     * 获取用户认证学校信息
     */
    public function getVerifiedSchoolInfo()
    {
        try {
            // 获取token
            $token = $this->request->header('token', '');
            if (empty($token)) {
                return json(['code' => 401, 'msg' => '请先登录']);
            }

            // 验证token
            $payload = JwtUtil::verifyToken($token);
            if (!$payload) {
                return json(['code' => 401, 'msg' => 'token无效']);
            }

            $userId = $payload['user_id'];

            // 查询用户认证学校信息
            $user = Db::name('user')
                ->field('id, username, status, verified_university_id')
                ->where('id', $userId)
                ->find();

            if (!$user) {
                return json(['code' => 404, 'msg' => '用户不存在']);
            }

            $result = [
                'user_id' => $user['id'],
                'username' => $user['username'],
                'status' => $user['status'],
                'verified_university_id' => $user['verified_university_id'],
                'verified_school_info' => null
            ];

            // 如果用户有认证学校ID，获取详细信息
            if ($user['verified_university_id']) {
                $verifiedSchoolInfo = Db::table('beijing_universities')
                    ->field('id, district, full_name, short_name, university_short_name, university_name, logo_url, university_id')
                    ->where('university_id', $user['verified_university_id'])
                    ->find();

                if ($verifiedSchoolInfo) {
                    $result['verified_school_info'] = $verifiedSchoolInfo;
                }
            }

            return json(['code' => 200, 'msg' => '获取成功', 'data' => $result]);

        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => '获取失败：' . $e->getMessage()]);
        }
    }
}