# Windows定时任务安装脚本
# 使用方法：以管理员身份运行PowerShell，然后执行此脚本

param(
    [string]$ProjectPath = (Get-Location).Path
)

Write-Host "=========================================" -ForegroundColor Green
Write-Host "树洞项目Windows定时任务安装脚本" -ForegroundColor Green
Write-Host "项目路径：$ProjectPath" -ForegroundColor Green
Write-Host "=========================================" -ForegroundColor Green

# 检查是否以管理员身份运行
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "错误：请以管理员身份运行PowerShell" -ForegroundColor Red
    exit 1
}

# 检查PHP是否可用
try {
    $phpVersion = php -v
    Write-Host "✅ PHP可用：$($phpVersion.Split("`n")[0])" -ForegroundColor Green
} catch {
    Write-Host "❌ 错误：找不到PHP命令" -ForegroundColor Red
    exit 1
}

# 检查项目目录
if (-not (Test-Path "$ProjectPath\think")) {
    Write-Host "❌ 错误：找不到ThinkPHP项目文件" -ForegroundColor Red
    exit 1
}

# 定义任务列表
$tasks = @(
    @{
        Name = "TreeHole-SecurityScan"
        Description = "树洞安全扫描"
        Command = "php"
        Arguments = "think security:scan"
        Trigger = "Daily"
        Time = "02:00"
    },
    @{
        Name = "TreeHole-CacheMonitor"
        Description = "树洞缓存监控"
        Command = "php"
        Arguments = "think cache:monitor"
        Trigger = "Every30Minutes"
        Time = "00:30"
    },
    @{
        Name = "TreeHole-WeeklyReport"
        Description = "树洞周报生成"
        Command = "php"
        Arguments = "think report:weekly"
        Trigger = "Weekly"
        Time = "09:00"
        Day = "Monday"
    },
    @{
        Name = "TreeHole-EventCleanup"
        Description = "树洞事件清理"
        Command = "php"
        Arguments = "think security:monitor --clean"
        Trigger = "Daily"
        Time = "06:00"
    }
)

Write-Host "`n即将创建以下定时任务：" -ForegroundColor Yellow
foreach ($task in $tasks) {
    Write-Host "- $($task.Name): $($task.Description)" -ForegroundColor Cyan
}

$confirm = Read-Host "`n是否继续安装？(Y/N)"
if ($confirm -ne "Y" -and $confirm -ne "y") {
    Write-Host "用户取消安装" -ForegroundColor Yellow
    exit 0
}

# 创建任务
foreach ($task in $tasks) {
    Write-Host "`n正在创建任务：$($task.Name)" -ForegroundColor Yellow
    
    try {
        # 删除已存在的同名任务
        try {
            Unregister-ScheduledTask -TaskName $task.Name -Confirm:$false -ErrorAction SilentlyContinue
        } catch {}
        
        # 创建动作
        $action = New-ScheduledTaskAction -Execute $task.Command -Argument $task.Arguments -WorkingDirectory $ProjectPath
        
        # 创建触发器
        switch ($task.Trigger) {
            "Daily" {
                $trigger = New-ScheduledTaskTrigger -Daily -At $task.Time
            }
            "Hourly" {
                # 每30分钟执行一次
                $trigger = New-ScheduledTaskTrigger -Once -At $task.Time -RepetitionInterval (New-TimeSpan -Minutes 30) -RepetitionDuration (New-TimeSpan -Days 365)
            }
            "Every30Minutes" {
                # 每30分钟执行一次
                $trigger = New-ScheduledTaskTrigger -Once -At $task.Time -RepetitionInterval (New-TimeSpan -Minutes 30) -RepetitionDuration (New-TimeSpan -Days 365)
            }
            "Weekly" {
                $trigger = New-ScheduledTaskTrigger -Weekly -DaysOfWeek $task.Day -At $task.Time
            }
        }
        
        # 创建设置
        $settings = New-ScheduledTaskSettingsSet -AllowStartIfOnBatteries -DontStopIfGoingOnBatteries -StartWhenAvailable
        
        # 创建主体（使用当前用户）
        $principal = New-ScheduledTaskPrincipal -UserId $env:USERNAME -LogonType Interactive
        
        # 注册任务
        Register-ScheduledTask -TaskName $task.Name -Action $action -Trigger $trigger -Settings $settings -Principal $principal -Description $task.Description
        
        Write-Host "✅ 任务 $($task.Name) 创建成功" -ForegroundColor Green
        
    } catch {
        Write-Host "❌ 任务 $($task.Name) 创建失败：$($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host "`n=========================================" -ForegroundColor Green
Write-Host "定时任务安装完成！" -ForegroundColor Green
Write-Host "`n📋 已创建的任务：" -ForegroundColor Yellow
Get-ScheduledTask | Where-Object {$_.TaskName -like "TreeHole-*"} | Select-Object TaskName, State | Format-Table

Write-Host "`n🛠️ 管理命令：" -ForegroundColor Yellow
Write-Host "查看任务：Get-ScheduledTask | Where-Object {`$_.TaskName -like 'TreeHole-*'}" -ForegroundColor Cyan
Write-Host "启动任务：Start-ScheduledTask -TaskName 'TreeHole-SecurityScan'" -ForegroundColor Cyan
Write-Host "停止任务：Stop-ScheduledTask -TaskName 'TreeHole-SecurityScan'" -ForegroundColor Cyan
Write-Host "删除任务：Unregister-ScheduledTask -TaskName 'TreeHole-SecurityScan'" -ForegroundColor Cyan

Write-Host "`n📊 查看任务执行历史：" -ForegroundColor Yellow
Write-Host "打开 '任务计划程序' → 找到对应任务 → 查看 '历史记录' 选项卡" -ForegroundColor Cyan

Write-Host "`n⚠️ 注意事项：" -ForegroundColor Yellow
Write-Host "1. 确保计算机在任务执行时间处于开机状态" -ForegroundColor White
Write-Host "2. 如果更改项目路径，需要重新运行此脚本" -ForegroundColor White
Write-Host "3. 可以在任务计划程序中手动调整任务设置" -ForegroundColor White
Write-Host "=========================================" -ForegroundColor Green
