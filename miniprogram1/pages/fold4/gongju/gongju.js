// pages/fold4/gongju/gongju.js
// 引入角色管理器
import roleManager from '../../../utils/roleManager';
// 引入分享配置工具
const { getShareAppMessageConfig, getShareTimelineConfig } = require('../../../utils/shareConfig.js');

Page({


  /**
   * 页面的初始数据
   */
  data: {
    grid2: [
      { number: 3, text: "上传资料", icon: "/images/shangchuan.png",url:"/pages/fold3/ziliao/ziliao"},
      { number: 3, text: "下载资料", icon: "/images/xiazai.png",url:"/pages/fold3/ziliao/ziliao"}
      // { number: 3, text: "意见建议", icon: "/images/fabusuoyin.png",url:"/pages/fold3/yijian/yijian"},
      // { number: 4, text: "学生认证", icon: "/images/xuesheng.png",url:"/pages/fold3/student/student"}
    ],
      grid3: [
        { number: 1, text: "沙河地图", icon: "/images/ditudaohang.png",url:"/pages/fold3/map/map"},
        { number: 1, text: "校车时刻表", icon: "/images/xiaoche-2.png",url:"/pages/fold3/xiaoche/xiaoche"},
        { number: 2, text: "航兵生存指南", icon: "/images/yanghushouce.png",url:"/pages/fold3/zhinan/zhinan"},
        { number: 3, text: "航概刷题", icon: "/images/gailun.jpeg", type: "miniProgram", appId: "wxf9b63b2783b573f3"},
        // { number: 3, text: "更新公告", icon: "/images/gengxingonggao.png",url:"/pages/fold3/gengxin/gengxin"},
        // { number: 4, text: "关于我们", icon: "/images/weixiao.png",url:"/pages/fold3/guanyu/guanyu"},
        // { number: 1, text: "隐私政策", icon: "/images/a-yinsisimibaomi.png",url:"/pages/fold3/yinsi/yinsi"},
        // { number: 2, text: "集市协议", icon: "/images/xieyiguanli.png",url:"/pages/fold3/xieyi/xieyi"},
        // { number: 3, text: "集市规范", icon: "/images/guifan.png",url:"/pages/fold3/guifan/guifan"},
      ],
      // grid4: [{number: 1, text: "退出登录", icon: "/images/guifan.png"}]
      grid4: [
        { number: 1, text: "北航Q群", icon: "/images/qie.png",url:"/pages/foldqun/qq/qq"},
        { number: 2, text: "社团群聊", icon: "/images/xingqiu.png",url:"/pages/foldqun/shetuan/shetuan"},
        { number: 3, text: "活动专区", icon: "/images/huodong.png",url:"/pages/foldqun/activity/activity", badge: 0},
        { number: 4, text: "官方公众号", icon: "/images/gongzhonghao.png",url:"/pages/foldqun/official/official"},
      ],
      grid5: [
        { number: 2, text: "意见(必回）", icon: "/images/youxiang.png",url:"/pages/foldshare/yijian/yijian"},
        { number: 3, text: "生活服务", icon: "/images/gongneng.png",url:"/pages/foldqun/life/life"}
        // ,{ number: 2, text: "蓝协小店(测试)", icon: "/images/zhiyuanzhefuwu.png",url:"/pages/foldshare/lantian/lantian"}
      ],
      grid6: [
        { number: 1, text: "食堂评分", icon: "/images/shitang.png",url:"/pages/canteen/list/index"},
        { number: 2, text: "了然几分", icon: "/images/liaoran.png",url:"/pages/liaoran/list/index"},
        { number: 3, text: "专业推荐", icon: "/images/zhuanye.png",url:"/pages/major/list/index"},
        { number: 4, text: "课程评价", icon: "/images/kecheng.png",url:"/pages/course/index"}
      ]
  },
  onItemTap4(e) {
    const index = e.currentTarget.dataset.index;
    const url = e.currentTarget.dataset.url;

    // 判断是否是前两个按钮（index从0开始计数，所以0和1是前两个按钮）
    if (index < 4) {
      // 前两个按钮需要基础权限
      if (roleManager.hasBasePermission()) {
        // 如果点击的是活动专区按钮
        if (index === 2) {
          // 先清除所有红点标记，再跳转页面
          // 清除活动专区按钮上的badge（本地UI更新）
          const grid4 = this.data.grid4;
          grid4[index].badge = 0;
          this.setData({ grid4 });

          // 使用全局方法清除所有活动相关的红点和角标
          getApp().globalData.clearActivityBadge();

          // 直接跳转到活动页面，让活动页面处理最后查看时间
          wx.navigateTo({
            url: url + '?from=gongju' // 传递来源参数
          });
        } else {
          wx.navigateTo({ url });
        }
      } else {
        wx.showToast({
          title: '只有本校生可以查看，请先完成学生认证',
          icon: 'none'
        });
      }
    } else {
      // 第三个及之后的按钮不做权限限制
      wx.navigateTo({ url });
    }
  },

  // 修改grid3的点击处理函数
  onGrid3Tap(e) {
    const index = e.currentTarget.dataset.index;
    const item = this.data.grid3[index];

    if (item.type === 'miniProgram') {
      wx.navigateToMiniProgram({
        appId: item.appId,
        success: function(res) {
          console.log('跳转成功')
        },
        fail: function(err) {
          console.log('跳转失败', err)
          // 只有在不是用户取消的情况下才显示提示
          if (err.errMsg.indexOf('cancel') === -1) {
            wx.showToast({
              title: '跳转失败',
              icon: 'none'
            })
          }
        }
      })
    } else {
      wx.navigateTo({ url: item.url });
    }
  },

  // grid6的点击处理函数
  onGrid6Tap(e) {
    const index = e.currentTarget.dataset.index;
    const item = this.data.grid6[index];

    // 所有按钮都有功能，可以直接跳转
    if (item.url) {
      wx.navigateTo({ url: item.url });
    }
  },

  // 检查是否有新活动通知
  checkActivityNotification() {
    // 检查用户是否订阅了活动通知
    const isSubscribed = wx.getStorageSync('activitySubscribe');
    if (isSubscribed === false) return; // 如果用户关闭了订阅，则不显示提示

    // 获取未读活动数量
    const unreadCount = wx.getStorageSync('unreadActivityCount') || 0;

    // 更新grid4中的活动专区badge
    if (unreadCount > 0) {
      const grid4 = this.data.grid4;
      grid4[2].badge = unreadCount; // 活动专区在grid4中的索引为2
      this.setData({ grid4 });
    }
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {

  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    const app = getApp();
    // 只在用户已登录时才检查新活动
    if (app.globalData.isLoggedIn && app.globalData.user_id) {
      // 每次页面显示时检查新活动
      app.globalData.checkNewActivities();
      // 检查活动通知
      this.checkActivityNotification();
    }

    // 更新所有通知
    app.globalData.updateAllNotifications();
    // 更新未读消息数Badge
    if (app.globalData.websocket) {
      app.globalData.websocket.updateBadgeOnShow();
    }
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },

  /**
   * 当用户点击该页面对应的TabBar项时触发
   */
  onTabItemTap: function(item) {
    const app = getApp();
    // 只在用户已登录时检查新活动
    if (app.globalData.isLoggedIn && app.globalData.user_id) {
      // 检查新活动
      app.globalData.checkNewActivities();
      // 检查活动通知
      this.checkActivityNotification();
    }
  },

  // 添加导航方法
  navigateTo(e) {
    const url = e.currentTarget.dataset.url
    wx.navigateTo({
      url
    })
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return getShareAppMessageConfig({
      pageName: '实用工具'
    });
  },

  /**
   * 用户点击右上角分享到朋友圈
   */
  onShareTimeline() {
    return getShareTimelineConfig({
      pageName: '实用工具'
    });
  }
})