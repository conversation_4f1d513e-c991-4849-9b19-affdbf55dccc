/**
 * 通用消息页面
 * 使用likes页面的UI样式，但适配新的数据结构
 */

const { handleMessageClick } = require('../../../utils/message-config')

Page({
  /**
   * 页面的初始数据
   */
  data: {
    config: {
      // 初始空配置，避免显示默认内容
      emptyConfig: {
        icon: '',
        text: ''
      }
    },
    pageType: 'notifications',
    statusBarHeight: 0,
    messages: [],
    loading: true,
    loadingMore: false,
    hasMore: true,
    showExpandHistory: false,
    loadingHistory: false,
    expandButtonText: '历史',
    isInitialized: false, // 标记是否已初始化
    isFromBackground: false // 标记是否从后台返回
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 获取状态栏高度
    const systemInfo = wx.getSystemInfoSync()

    // 从页面参数获取消息类型
    const type = options.type || 'notifications'

    // 设置配置
    const config = this.getMessageConfig(type)

    this.setData({
      config,
      pageType: type,
      statusBarHeight: systemInfo.statusBarHeight
    }, () => {
      // 在 setData 完成后初始化消息列表组件
      this.initMessageList()
    })

    // 设置导航栏标题
    wx.setNavigationBarTitle({
      title: config.title
    })
  },

  /**
   * 获取消息配置
   */
  getMessageConfig(type) {
    const configs = {
      likes: {
        title: '收到的赞',
        type: 'likes',
        emptyConfig: {
          icon: '/images/aixin.png',
          text: '还没有收到点赞哦~'
        }
      },
      replies: {
        title: '收到的回复',
        type: 'replies',
        emptyConfig: {
          icon: '/images/pinglun.png',
          text: '还没有收到回复哦~'
        }
      },
      notifications: {
        title: '系统通知',
        type: 'notifications',
        emptyConfig: {
          icon: '/images/xiaoxi.png',
          text: '暂无通知'
        }
      }
    }
    return configs[type] || configs.notifications
  },

  /**
   * 初始化消息列表组件
   */
  initMessageList() {
    // 等待组件加载完成和配置设置完成
    setTimeout(() => {
      const messageList = this.selectComponent('#messageList')
      if (messageList) {
        console.log('开始初始化加载，类型:', this.data.pageType)
        // 初始化加载消息数据（使用智能显示逻辑）
        messageList.initLoad()
        // 标记已初始化
        this.setData({ isInitialized: true })
      } else {
        console.error('未找到 messageList 组件')
      }
    }, 200) // 增加延迟时间
  },

  /**
   * 处理消息数据变化
   */
  onMessageChange(e) {
    const { messages, loading, showExpandHistory, loadingHistory, hasMore, loadingMore, expandButtonText } = e.detail

    // 获取消息类型的中文名称
    let messageTypeName = ''
    if (this.data.config.type === 'likes') {
      messageTypeName = '点赞'
    } else if (this.data.config.type === 'replies') {
      messageTypeName = '评论'
    } else {
      messageTypeName = '通知'
    }



    this.setData({
      messages: messages || [],
      loading: loading !== false,
      showExpandHistory: showExpandHistory || false,
      loadingHistory: loadingHistory || false,
      hasMore: hasMore !== false,
      loadingMore: loadingMore || false,
      messageTypeName: messageTypeName,
      expandButtonText: expandButtonText || '历史'
    })
  },

  /**
   * 处理消息项点击
   */
  onItemClick(e) {
    const { item } = e.detail
    this.handleMessageClick(item)
  },

  /**
   * 处理加载更多
   */
  onLoadMore() {
    const messageList = this.selectComponent('#messageList')
    if (messageList) {
      messageList.onLoadMore()
    }
  },

  /**
   * 处理下拉刷新
   */
  onRefresh() {
    const messageList = this.selectComponent('#messageList')
    if (messageList) {
      // 下拉刷新时强制使用正常加载模式，确保显示最新消息
      messageList.loadMessages(true, true)
    }
  },

  /**
   * 处理消息点击
   */
  handleMessageClick(item) {
    console.log('点击消息:', item)

    // 使用统一的消息处理工具
    handleMessageClick(item, this.data.pageType)
  },

  /**
   * 刷新列表
   */
  refreshList() {
    const messageList = this.selectComponent('#messageList')
    if (messageList) {
      // 手动刷新时使用正常加载模式，确保显示最新消息
      messageList.loadMessages(true, true)
    }
  },

  /**
   * 返回按钮点击事件
   */
  onBack() {
    wx.navigateBack()
  },

  /**
   * 更多按钮点击事件
   */
  onMoreClick() {
    wx.showActionSheet({
      itemList: ['标记全部已读', '刷新'],
      success: (res) => {
        if (res.tapIndex === 0) {
          // 标记全部已读
          this.markAllAsRead()
        } else if (res.tapIndex === 1) {
          // 刷新列表
          this.refreshList()
        }
      }
    })
  },

  /**
   * 展开历史消息
   */
  onExpandHistory() {
    const messageList = this.selectComponent('#messageList')
    if (messageList && messageList.expandHistory) {
      messageList.expandHistory()
    }
  },

  /**
   * 标记全部已读
   */
  markAllAsRead() {
    wx.showToast({
      title: '已标记为已读',
      icon: 'success'
    })
    // 通过message-list组件处理
    const messageList = this.selectComponent('#messageList')
    if (messageList && messageList.markAllAsRead) {
      messageList.markAllAsRead()
    }
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 只有在已初始化且从其他页面返回时才刷新数据，避免重复加载
    if (this.data.isInitialized && this.data.isFromBackground) {
      const messageList = this.selectComponent('#messageList')
      if (messageList) {
        messageList.initLoad()
      }
      // 重置标志
      this.setData({ isFromBackground: false })
    }
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {
    // 标记页面进入后台
    this.setData({ isFromBackground: true })
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    const messageList = this.selectComponent('#messageList')
    if (messageList) {
      // 下拉刷新时强制使用正常加载模式，确保显示最新消息
      messageList.loadMessages(true, true)
    }
    wx.stopPullDownRefresh()
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
    const messageList = this.selectComponent('#messageList')
    if (messageList) {
      messageList.onLoadMore()
    }
  },


})
