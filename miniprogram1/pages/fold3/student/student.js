// pages/fold3/student/student.js
const { navigateBack } = require('../../../utils/navigation');

Page({

  /**
   * 页面的初始数据
   */
  data: {
    status: '',
    schoolLogo: '', // 默认头像，在onShow中初始化
    selected_school: null // 用户选择的学校信息
  },
  onClickLeft() {
    navigateBack();
  },


  upimage: function () {
    const that = this;
    wx.chooseImage({
      count: 1, // 只允许上传一张图片
      sizeType: ['original', 'compressed'],
      sourceType: ['album', 'camera'],
      success(res) {
        const tempFilePaths = res.tempFilePaths;
        const id = getApp().globalData.user_id;
        const timestamp = Date.now();
        const newImageName = `${id}_${timestamp}.png`;

        // 上传图片
        wx.uploadFile({
          url: getApp().globalData.wangz + '/upload/uploadToCos', // 服务器接口地址
          filePath: tempFilePaths[0],
          name: 'file',
          formData: {
            type: 'renzheng'
          },
          header: {
            'token': wx.getStorageSync('access_token')
          },
          success(uploadRes) {
            const data = JSON.parse(uploadRes.data);

            if (data.code === 200) {
              // 图片上传成功后，提交认证申请
              that.submitAuthApplication(data.data.url);
            } else {
              wx.showToast({
                title: data.msg || '图片上传失败',
                icon: 'none',
                duration: 2000
              });
            }
          },
          fail(err) {
            wx.showToast({
              title: '上传失败，请重试',
              icon: 'none',
              duration: 2000
            });
          }
        });
      }
    });
  },

  // 提交认证申请
  submitAuthApplication(imageUrl) {
    const userId = wx.getStorageSync('user_id') || getApp().globalData.user_id;
    const selectedSchool = wx.getStorageSync('selected_school');

    if (!selectedSchool) {
      wx.showToast({
        title: '请先在个人资料中选择学校',
        icon: 'none',
        duration: 3000
      });
      return;
    }

    wx.showLoading({
      title: '提交认证申请...',
      mask: true
    });

    wx.request({
      url: getApp().globalData.wangz + '/auth/submitApplication',
      method: 'POST',
      header: {
        'content-type': 'application/x-www-form-urlencoded',
        'token': wx.getStorageSync('access_token')
      },
      data: {
        user_id: userId,
        school_id: selectedSchool.id,
        school_name: selectedSchool.full_name || selectedSchool.short_name,
        image_url: imageUrl
      },
      success: (res) => {
        wx.hideLoading();
        if (res.data.code === 200) {
          wx.showToast({
            title: '认证申请已提交，请等待审核',
            icon: 'success',
            duration: 3000
          });
        } else {
          wx.showToast({
            title: res.data.msg || '提交失败',
            icon: 'none',
            duration: 2000
          });
        }
      },
      fail: (err) => {
        wx.hideLoading();
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none',
          duration: 2000
        });
      }
    });
  },


  stuverify: function () {
    const that = this;

    // 检查用户是否已经授权获取位置信息
    wx.getSetting({
      success: (res) => {
        if (res.authSetting['scope.userLocation']) {
          // 用户已授权，直接获取位置
          that.getLocation();
        } else {
          // 请求用户授权
          wx.authorize({
            scope: 'scope.userLocation',
            success: () => {
              // 用户同意授权，获取位置
              that.getLocation();
            },
            fail: () => {
              // 用户拒绝授权，打开设置界面引导用户授权
              wx.showModal({
                title: '需要授权位置信息',
                content: '请在设置中打开位置信息授权',
                success: (res) => {
                  if (res.confirm) {
                    // 用户点击确定，打开设置页面
                    wx.openSetting({
                      success: (res) => {
                        if (res.authSetting['scope.userLocation']) {
                          // 用户在设置页面授权，重新获取位置
                          that.getLocation();
                        }
                      }
                    });
                  }
                }
              });
            }
          });
        }
      }
    });
  },

  getLocation: function () {
    const that = this;

    wx.getLocation({
      type: 'gcj02', // 返回可以用于 wx.openLocation 的经纬度
      success(res) {
        const latitude = res.latitude;
        const longitude = res.longitude;
        // 进行位置校验
        that.checkLocation(latitude, longitude);
      },
      fail(err) {
        // 处理定位失败的情况
        wx.showToast({
          title: '无法获取位置，请检查权限设置',
          icon: 'none',
          duration: 2000
        });
      }
    });
  },

  // 校验位置是否在两个校区范围内的函数
  checkLocation: function (userLat, userLng) {
    // 设定两个校区的中心点和半径
    const campus1 = { latitude: 39.981276, longitude: 116.348013, radius: 800 }; // 校区1的经纬度和半径
    const campus2 = { latitude: 40.153513, longitude: 116.27071, radius: 800 };  // 校区2的经纬度和半径

    // 校验是否在校区1范围内
    const distanceToCampus1 = this.getDistance(userLat, userLng, campus1.latitude, campus1.longitude);

    // 校验是否在校区2范围内
    const distanceToCampus2 = this.getDistance(userLat, userLng, campus2.latitude, campus2.longitude);

    if (distanceToCampus1 <= campus1.radius || distanceToCampus2 <= campus2.radius) {
      // 用户在任意一个校区范围内，认证通过
      wx.showToast({
        title: '认证成功',
        icon: 'success',
        duration: 2000
      });

      // 更新用户的认证状态
      this.updateUserStatus();

    } else {
      // 用户不在任意校区范围内，认证失败
      wx.showToast({
        title: '认证失败，您不在指定范围内',
        icon: 'none',
        duration: 2000
      });
    }
  },

  // 计算两点之间的距离函数
  getDistance: function (lat1, lng1, lat2, lng2) {
    const radLat1 = lat1 * Math.PI / 180.0;
    const radLat2 = lat2 * Math.PI / 180.0;
    const a = radLat1 - radLat2;
    const b = (lng1 - lng2) * Math.PI / 180.0;
    let s = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a / 2), 2) +
      Math.cos(radLat1) * Math.cos(radLat2) * Math.pow(Math.sin(b / 2), 2)));
    s = s * 6378137.0; // 地球半径，单位米
    return s;
  },

  // 更新用户认证状态的函数（可选）
  updateUserStatus: function () {
    const that = this;  // 记录页面实例，便于在回调中使用
    // 发起请求更新服务器上的用户状态
    wx.request({
      url: getApp().globalData.wangz + '/user/updateStatus', // 你的服务器API地址
      method: 'POST',
      data: {
        id: getApp().globalData.user_id, // 假设你在data中存储了用户ID
        status: 'temporary_verified' // 设置状态为临时认证
      },
      header: {
        'Content-Type': 'application/json' // 确保使用正确的Content-Type
      },
      success: function (res) {
        if (res.data.success) {
          wx.showToast({
            title: '临时认证成功',
            icon: 'success',
            duration: 2000
          });
          // 更新本地缓存和全局数据，以便后续使用新角色权限
          const newStatus = 'temporary_verified';
          wx.setStorageSync('status', newStatus);
          getApp().globalData.status = newStatus;
          // 同时更新本页面显示状态
          that.setData({ status: '临时认证' });
        } else {
          wx.showToast({
            title: '认证状态更新失败',
            icon: 'none',
            duration: 2000
          });
        }
      },
      fail: function (err) {
        wx.showToast({
          title: '网络错误，请稍后重试',
          icon: 'none',
          duration: 2000
        });
      }
    });
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 每次显示页面时获取最新的认证状态
    const status = wx.getStorageSync("status") || "unverified";

    // 状态映射
    let statusText = "";
    switch(status) {
      case "unverified":
        statusText = "未认证";
        break;
      case "verified":
        statusText = "已认证";
        break;
      case "temporary_verified":
        statusText = "临时认证";
        break;
      default:
        statusText = status;
    }

    // 获取学校头像
    const selectedSchool = wx.getStorageSync('selected_school');
    let schoolLogo = getApp().globalData.default_school_icon; // 使用全局默认头像

    if (selectedSchool && selectedSchool.logo_url) {
      schoolLogo = selectedSchool.logo_url;
    }

    // 更新页面显示
    this.setData({
      status: statusText,
      schoolLogo: schoolLogo,
    });
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },


})