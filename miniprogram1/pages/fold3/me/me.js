// pages/fold3/me/me.js
import { loginManager } from '../../../utils/loginManager';
import eventBus from '../../../utils/eventBus';
// 引入角色管理器
import roleManager from '../../../utils/roleManager';
// 引入分享配置工具
const { getShareAppMessageConfig, getShareTimelineConfig } = require('../../../utils/shareConfig.js');

Page({
  

  /**
   * 页面的初始数据
   */
  data: {name1:'Hi~点击此处登录',selectedImagePath: 'https://www.bjgaoxiaoshequ.store/images/weixiao.png',
    showGuideHint: false,
    guideStep: 1,
    guideClosing: false,
    showLongPressHint: false,
    showNicknameModal: false,  // 是否显示昵称修改弹窗
    tempNickname: '',         // 临时昵称（正在编辑）
    nicknameLength: 0,        // 昵称长度
    showNicknameCounter: false, // 是否显示计数器
    showOfficialAccountTip: true, // 是否显示公众号提示
    showCloseConfirmModal: false, // 是否显示关闭确认弹窗
    showServiceQRCode: false, // 是否显示服务号二维码弹窗
    serviceQRCodeUrl: 'https://www.bjgaoxiaoshequ.store/tupian/fwhqrcode.jpg', // 服务号二维码图片URL
    grid1: [
    { number: 1, text: "我发布的", icon: "/images/dangshidati-01.png",url:"/pages/foldshare/mypub/mypub",msgCount:0, showBadge: false},
    { number: 2, text: "点赞我的", icon: "/images/aixin.png",url:"/pages/common/message-page/message-page?type=likes",msgCount:0, showBadge: false},
    { number: 3, text: "评论我的", icon: "/images/liaotianzidonghuifu.png",url:"/pages/common/message-page/message-page?type=replies",msgCount:0, showBadge: false },
    { number: 4, text: "消息通知", icon: "/images/xingqiu.png",url:"/pages/common/message-page/message-page?type=notifications",msgCount:0, showBadge: false }
    ],
    grid2: [
      { number: 3, text: "上传资料", icon: "/images/shangchuan.png",url:"/pages/fold3/ziliao/ziliao"},
      { number: 3, text: "下载资料", icon: "/images/xiazai.png",url:"/pages/fold3/ziliao/ziliao"}
      // { number: 3, text: "意见建议", icon: "/images/fabusuoyin.png",url:"/pages/fold3/yijian/yijian"},
      // { number: 4, text: "学生认证", icon: "/images/xuesheng.png",url:"/pages/fold3/student/student"}
    ],
      grid3: [
        { number: 1, text: "沙河地图", icon: "/images/ditudaohang.png",url:"/pages/fold3/map/map"},
        { number: 2, text: "自定义课表", icon: "/images/kebiao.png",url:"/pages/fold3/subject/subject"},
        { number: 1, text: "校车时刻表", icon: "/images/xiaoche-2.png",url:"/pages/fold3/xiaoche/xiaoche"},
        { number: 2, text: "校车抢票攻略", icon: "/images/gonglve.png",url:"/pages/fold3/qiangpiao/qiangpiao"},
        // { number: 3, text: "更新公告", icon: "/images/gengxingonggao.png",url:"/pages/fold3/gengxin/gengxin"},
        // { number: 4, text: "关于我们", icon: "/images/weixiao.png",url:"/pages/fold3/guanyu/guanyu"},
        // { number: 1, text: "隐私政策", icon: "/images/a-yinsisimibaomi.png",url:"/pages/fold3/yinsi/yinsi"},
        // { number: 2, text: "集市协议", icon: "/images/xieyiguanli.png",url:"/pages/fold3/xieyi/xieyi"},
        // { number: 3, text: "集市规范", icon: "/images/guifan.png",url:"/pages/fold3/guifan/guifan"},
      ],
      // grid4: [{number: 1, text: "退出登录", icon: "/images/guifan.png"}]
      grid4: [
        { number: 1, text: "更新公告", icon: "/images/yewushouce.png",url:"/pages/fold3/gengxin/gengxin"},
        { number: 2, text: "关于我们", icon: "/images/weixiao.png",url:"/pages/fold3/guanyu/guanyu"},
        { number: 3, text: "学生认证", icon: "/images/xuesheng.png",url:"/pages/fold3/student/student"},
        { number: 4, text: "个人资料", icon: "/images/gerenziliao.png",url:"/pages/fold3/geren/geren"},
      ],
      grid5: [
        { number: 1, text: "系统设置", icon: "/images/shezhi.png", url: "/pages/fold3/setting/setting" },
        { number: 2, text: "", icon: "", url: "" },
        { number: 3, text: "", icon: "", url: "" },
        { number: 4, text: "", icon: "", url: "" },
      ],
      userRole: '',
      isLoggedIn: false
  },
  onItemTap4(e) {
    const index = e.currentTarget.dataset.index;
    const url = e.currentTarget.dataset.url;
    
    // 前两个按钮（index从0开始计数，所以0和1是前两个按钮）
    if (index < 2) {
      // 前两个按钮需要verified权限
      if (roleManager.hasBasePermission()) {
        wx.navigateTo({ url });
      } else {
        wx.showToast({
          title: '只有本校生可以查看，请先完成学生认证',
          icon: 'none'
        });
      }
    } else {
      // 第三个及之后的按钮不做权限限制
      wx.navigateTo({ url });
    }
  },
  
  onItemTap5(e) {
    const index = e.currentTarget.dataset.index;
    const url = e.currentTarget.dataset.url;
    
    if (url) {
      wx.navigateTo({ url });
    } else {
      // 如果URL为空，可以显示一个提示
      if (index === 0) {
        wx.showToast({
          title: '设置功能即将开放',
          icon: 'none'
        });
      }
    }
  },
  
  chooseAvatar: function(e) {
    const avatarUrl = e.detail.avatarUrl;  // 获取微信返回的头像路径
    const app = getApp();

    // 更新页面数据和全局头像路径
    this.setData({
      selectedImagePath: avatarUrl, // 更新本地头像路径
    });

    // 上传头像到服务器
    this.uploadAvatar(avatarUrl); 
  },

  /**
   * 上传头像到服务器（使用统一上传接口）
   */
  uploadAvatar: function(filePath) {
    var that = this;

    // 显示上传进度
    wx.showLoading({
      title: '头像上传中...'
    });

    wx.uploadFile({
      url: getApp().globalData.wangz + '/user/updateFaceUrlByPhone',
      filePath: filePath,
      name: 'face', // 服务器端接收的字段名
      formData: {
        'phone': getApp().globalData.phone,
      },
      header: {
        'token': wx.getStorageSync('access_token')
      },
      success(res) {
        wx.hideLoading();
        var data = JSON.parse(res.data);
        if (data.error_code === 0) {
          // 更新全局变量的 face_url 为从服务器返回的完整URL
          getApp().globalData.face_url = data.face_url;
          console.log('头像上传成功，新URL:', data.face_url);
          wx.setStorageSync('face_url', data.face_url);

          // 更新页面显示
          that.setData({
            selectedImagePath: data.face_url
          });

          wx.showToast({
            title: '头像上传成功',
            icon: 'success',
            duration: 2000
          });
        } else {
          wx.showToast({
            title: data.msg || '头像上传失败',
            icon: 'none',
            duration: 2000
          });
        }
      },
      fail(err) {
        wx.hideLoading();
        console.error('头像上传失败:', err);
        wx.showToast({
          title: '上传头像时出现异常',
          icon: 'none',
          duration: 2000
        });
      }
    });
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 页面加载时获取点赞列表
    this.updateUnreadCounts();
    this.updateUserInfo();

    // 添加WebSocket监听
    const app = getApp();
    if (app.globalData.websocket) {
      app.globalData.websocket.addListener(this.handleWebSocketMessage);
    }

    // 检查引导状态
    this.checkGuideStatus();
    
    // 检查公众号提示状态
    this.checkOfficialAccountTipStatus();
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    const app = getApp();

    // 每次显示页面时都重新获取未读消息数
    this.updateUnreadCounts();

    // 更新用户信息
    this.updateUserInfo();

    // 检查引导状态（如果弹窗显示则不检查）
    if (!this.data.showNicknameModal && !this.data.showCloseConfirmModal) {
      this.checkGuideStatus();
    }

    // 检查公众号提示状态
    this.checkOfficialAccountTipStatus();
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {
    // 页面卸载时移除WebSocket监听
    const app = getApp();
    if (app.globalData.websocket) {
      app.globalData.websocket.removeListener(this.handleWebSocketMessage);
    }
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return getShareAppMessageConfig({
      pageName: '个人中心'
    });
  },

  /**
   * 用户点击右上角分享到朋友圈
   */
  onShareTimeline() {
    return getShareTimelineConfig({
      pageName: '个人中心'
    });
  },

  // 处理WebSocket消息
  handleWebSocketMessage(message) {
    if (message.type === 'notification' && message.data) {
      const notificationType = message.data.type;
      // 收到点赞、评论或回复通知时更新未读消息数
      if (notificationType === 'like' || notificationType === 'comment' || notificationType === 'reply') {
        this.updateUnreadCounts();
      }
    }
  },

  // 更新未读消息数
  updateUnreadCounts() {
    const app = getApp();
    if (!app.globalData.user_id) return;

    // 构造请求参数
    const requestData = {
      user_id: app.globalData.user_id,
      type: 'like',
      show_all: 0
    };

    // 同时查询点赞、评论和回复通知状态
    Promise.all([
      // 查询点赞通知
      new Promise((resolve, reject) => {
        wx.request({
          url: `${app.globalData.wangz}/notification/getMessages`,
          method: 'POST',
          header: {
            'content-type': 'application/x-www-form-urlencoded',
            'token': wx.getStorageSync('access_token')
          },
          data: {
            type: 'likes',
            page: 1,
            page_size: 1
          },
          success: (res) => {
            if (res.data.code === 200) {
              // 使用unread_count字段获取未读总数
              app.globalData.unread = res.data.unread_count || 0;
              resolve();
            } else {
              resolve();
            }
          },
          fail: () => resolve()
        });
      }),
      
      // 查询评论通知
      new Promise((resolve, reject) => {
        wx.request({
          url: `${app.globalData.wangz}/notification/getMessages`,
          method: 'POST',
          header: {
            'content-type': 'application/x-www-form-urlencoded',
            'token': wx.getStorageSync('access_token')
          },
          data: {
            type: 'replies',
            page: 1,
            page_size: 1
          },
          success: (res) => {
            if (res.data.code === 200) {
              // 使用unread_count字段获取未读总数
              app.globalData.unreadComments = res.data.unread_count || 0;
              resolve();
            } else {
              console.error('获取评论通知失败:', res.data);
              resolve();
            }
          },
          fail: () => resolve()
        });
      }),
      
      // 查询系统通知
      new Promise((resolve, reject) => {
        wx.request({
          url: `${app.globalData.wangz}/notification/getMessages`,
          method: 'POST',
          header: {
            'content-type': 'application/x-www-form-urlencoded',
            'token': wx.getStorageSync('access_token')
          },
          data: {
            type: 'notifications',
            page: 1,
            page_size: 1
          },
          success: (res) => {
            if (res.data.code === 200) {
              // 使用unread_count字段获取未读总数
              app.globalData.unreadNotifications = res.data.unread_count || 0;
              resolve();
            } else {
              console.error('获取系统通知失败:', res.data);
              resolve();
            }
          },
          fail: () => resolve()
        });
      })
    ]).then(() => {
      // 更新grid1中的未读消息数
      this.updateGridNotifications();
    });
  },

  // 接收WebSocket通知时的回调
  onUnreadCountUpdate(unreadCount) {
    this.updateUnreadCounts();
  },

  // 更新用户信息显示
  updateUserInfo() {
    const app = getApp();
    const username = app.globalData.username || wx.getStorageSync('username');
    const face_url = app.globalData.face_url || wx.getStorageSync('face_url');
    const isLoggedIn = app.globalData.isLoggedIn || wx.getStorageSync('isLoggedIn');
    const status = roleManager.getUserRole();

    // 使用imageUtil处理头像URL
    const imageUtil = require('../../../utils/imageUtil.js');
    const processedFaceUrl = face_url ? imageUtil.processAvatarUrl(face_url) : '/images/weixiao.png';

    this.setData({
      name1: isLoggedIn ? 'Hi~' + username : 'Hi~点击此处登录',
      selectedImagePath: processedFaceUrl,
      userRole: status,
      isLoggedIn: isLoggedIn
    });
  },

  // 处理登录点击
  async handleLoginClick() {
    if (!this.data.isLoggedIn) {
      const success = await loginManager.login(true); // 传入true表示静默登录
      if (success) {
        // 登录成功后更新用户信息
        this.updateUserInfo();
        // 更新未读消息数量
        this.updateUnreadCounts();
      }
    }
  },

  // 处理昵称点击
  handleNameClick() {
    // 如果未登录，先执行登录后再显示弹窗
    if (!this.data.isLoggedIn) {
      loginManager.login(true).then(success => {
        if (success) {
          // 登录成功后更新用户信息
          this.updateUserInfo();
          // 更新未读消息数量
          this.updateUnreadCounts();
          
          // 登录成功后，延迟显示修改昵称弹窗
          setTimeout(() => {
            const username = wx.getStorageSync('username') || '';
            this.setData({
              showNicknameModal: true,
              tempNickname: username,
              nicknameLength: username.length
            });
          }, 500); // 延迟500毫秒，确保登录信息已更新
        }
      });
      return;
    }

    // 如果已登录，直接打开修改昵称弹窗
    const username = wx.getStorageSync('username') || '';
    this.setData({
      showNicknameModal: true,
      tempNickname: username,
      nicknameLength: username.length
    });
  },

  // 关闭昵称弹窗
  closeNicknameModal() {
    this.setData({
      showNicknameModal: false
    });
  },
  
  // 阻止点击事件冒泡
  preventTap() {
    // 只阻止冒泡，不做任何操作
  },
  
  // 阻止滚动事件
  preventScroll() {
    // 阻止页面滚动
    return false;
  },

  // 阻止点击事件冒泡到父元素
  stopPropagation(e) {
    // 微信小程序中不需要显式调用stopPropagation
    // 使用catchtap即可
  },

  // 监听昵称输入变化
  onNicknameInput(e) {
    const nickName = e.detail.value;
    // 如果输入为空，保持显示当前昵称，但更新计数器
    this.setData({
      nicknameLength: nickName.length
    });
    
    // 只有当输入不为空且不全是空格时，才更新临时昵称
    if (nickName && nickName.trim()) {
      this.setData({
        tempNickname: nickName
      });
    }
  },

  // 昵称输入框获得焦点
  onNicknameFocus() {
    this.setData({
      showNicknameCounter: true
    });
  },

  // 昵称输入框失去焦点
  onNicknameBlur() {
    this.setData({
      showNicknameCounter: false
    });
  },

  // 提交修改昵称
  submitNickname(e) {
    const nickName = e.detail.value.nickname;
    // 如果昵称为空或只包含空格，则不修改昵称
    if (!nickName || !nickName.trim()) {
      this.closeNicknameModal();
      return;
    }

    const app = getApp();
    const user_id = app.globalData.user_id;
    // 获取当前头衔和颜色
    const titlename = wx.getStorageSync('titlename') || '';
    const titlecolor = wx.getStorageSync('titlecolor') || 0;

    // 打印调试信息
    console.log("提交昵称修改:", {
      user_id,
      username: nickName,
      titlename,
      titlecolor
    });

    // 向服务器发送更新请求
    wx.request({
      url: app.globalData.wangz + '/user/updateUserinformById',
      method: 'POST',
      header: {
        'content-type': 'application/x-www-form-urlencoded'
      },
      data: {
        user_id: user_id,
        username: nickName,
        titlename: titlename, // 添加头衔参数
        titlecolor: titlecolor // 添加颜色参数
      },
      success: (res) => {
        console.log("服务器响应:", res.data);
        if (res.data.error_code === 0) {
          // 更新全局变量和本地存储
          app.globalData.username = nickName;
          wx.setStorageSync('username', nickName);
          
          // 更新页面显示，保持"Hi~"前缀
          this.setData({
            name1: 'Hi~' + nickName
          });
          
          wx.showToast({
            title: '昵称修改成功',
            icon: 'success',
            duration: 2000
          });
        } else {
          wx.showToast({
            title: res.data.message || '昵称修改失败',
            icon: 'error',
            duration: 2000
          });
        }
        this.closeNicknameModal();
      },
      fail: (err) => {
        console.error("请求失败:", err);
        wx.showToast({
          title: '网络错误，请稍后重试',
          icon: 'none',
          duration: 2000
        });
        this.closeNicknameModal();
      }
    });
  },

  // 退出登录
  async dianji2() {
    wx.showModal({
      title: '提示',
      content: '确定要退出登录吗？',
      success: async (res) => {
        if (res.confirm) {
          const success = await loginManager.logout();
          if (success) {
            // 更新界面
            this.updateUserInfo();
            // 移除TabBar消息徽章
            wx.removeTabBarBadge({
              index: 3
            });
            // 重置所有消息计数
            const grid1 = this.data.grid1.map(item => ({
              ...item,
              msgCount: 0
            }));
            this.setData({ grid1 });
            
            wx.showToast({
              title: '已退出登录',
              icon: 'success'
            });
          } else {
            wx.showToast({
              title: '退出登录失败',
              icon: 'error'
            });
          }
        }
      }
    });
  },

  // 检查引导状态
  checkGuideStatus() {
    const user_id = wx.getStorageSync('user_id');
    
    // 如果弹窗已显示，则不显示引导
    if (this.data.showNicknameModal || this.data.showCloseConfirmModal) {
      return;
    }
    
    if (!user_id) {
      return;
    }

    wx.request({
      url: getApp().globalData.wangz + '/guide_hint/getHintStatus',
      method: 'POST',
      data: {
        user_id: user_id,
        page_key: 'me_page'
      },
      header: {
        'content-type': 'application/x-www-form-urlencoded'
      },
      success: (res) => {
        if (res.data && res.data.code === 1) {
          if (res.data.data && res.data.data.should_show_hint) {
            this.setData({
              showGuideHint: true,
              guideStep: 1,
              guideClosing: false
            });
          }
        }
      },
      fail: (error) => {
        console.error('引导状态检查请求失败:', error);
      }
    });
  },

  // 关闭引导提示
  closeGuideHint() {
    const user_id = wx.getStorageSync('user_id');
    
    this.setData({
      guideClosing: true
    });

    wx.request({
      url: getApp().globalData.wangz + '/guide_hint/updateHintStatus',
      method: 'POST',
      data: {
        user_id: user_id,
        page_key: 'me_page',
        step_key: 'guide'
      },
      header: {
        'content-type': 'application/x-www-form-urlencoded'
      },
      success: (res) => {
        if (res.data && res.data.code === 1) {
          this.setData({
            showGuideHint: false,
            guideClosing: false
          });
        } else {
          this.setData({
            showGuideHint: false,
            guideClosing: false
          });
        }
      },
      fail: (error) => {
        console.error('更新引导状态请求失败:', error);
        this.setData({
          showGuideHint: false,
          guideClosing: false
        });
      }
    });
  },

  // 显示长按删除提示
  showLongPressGuide() {
    this.setData({
      showLongPressHint: true
    });
    setTimeout(() => {
      this.setData({
        showLongPressHint: false
      });
    }, 3000);
  },

  // 处理消息通知点击
  handleNotificationClick() {
    this.hideGuide();
    // 其他点击处理逻辑...
  },

  // 处理我发布的长按
  handleMyPubLongPress() {
    this.showLongPressGuide();
   
  },

  // 更新"我发布的"未读状态
  updateMyPubUnreadStatus(count) {
    const grid1 = this.data.grid1;
    if (grid1 && grid1[0]) {
      grid1[0].msgCount = count;
      this.setData({ grid1 });
    }
    
    // 如果所有消息都已读，移除TabBar badge
    if (count === 0) {
      wx.removeTabBarBadge({
        index: 3
      }).catch(() => {});
    }
  },

  // 更新grid1中的通知数量显示
  updateGridNotifications() {
    const app = getApp();
    const grid1 = [...this.data.grid1];
    
    // 直接使用全局数据，不检查本地标记
    const likeUnread = parseInt(app.globalData.unread || 0);
    
    // 更新"点赞我的"通知数
    if (grid1[1]) {
      grid1[1].msgCount = likeUnread;
      grid1[1].showBadge = likeUnread > 0;
    }
    
    // 更新"评论我的"通知数（只包含评论回复）
    if (grid1[2]) {
      const commentUnread = parseInt(app.globalData.unreadComments || 0);
      grid1[2].msgCount = commentUnread;
      grid1[2].showBadge = commentUnread > 0;
    }

    // 更新"消息通知"通知数（系统通知）
    if (grid1[3]) {
      const notificationUnread = parseInt(app.globalData.unreadNotifications || 0);
      grid1[3].msgCount = notificationUnread;
      grid1[3].showBadge = notificationUnread > 0;
    }
    
    // 计算总的未读消息数
    const totalUnread = likeUnread +
                        parseInt(app.globalData.unreadComments || 0) +
                        parseInt(app.globalData.unreadNotifications || 0);
    
    // 更新grid1显示
    this.setData({ grid1 });
    
    // 检查当前是否在TabBar页面
    const pages = getCurrentPages();
    const currentPage = pages[pages.length - 1];
    const isTabBarPage = currentPage && 
      ['pages/fold1/home/<USER>', 'pages/fold2/xuqiu/xuqiu', 
       'pages/fold3/me/me', 'pages/fold4/gongju/gongju'].includes(currentPage.route);
    
    // 只在TabBar页面更新徽章
    if (isTabBarPage) {
      if (totalUnread > 0) {
        wx.setTabBarBadge({
          index: 3,
          text: totalUnread.toString()
        }).catch(() => {});
      } else {
        wx.removeTabBarBadge({
          index: 3
        }).catch(() => {});
      }
    }
  },

  // 处理grid1项的点击
  onItemTap1(e) {
    const index = e.currentTarget.dataset.index;
    const url = e.currentTarget.dataset.url;
    const app = getApp();
    
    // 直接跳转到对应页面，不等待通知清除完成
    // 在各页面的onLoad中处理通知清除
    wx.navigateTo({ url });
  },

  // 检查公众号提示状态
  checkOfficialAccountTipStatus() {
    // 从本地存储获取状态
    const showOfficialAccountTip = wx.getStorageSync('showOfficialAccountTip');
    if (showOfficialAccountTip !== "") {
      this.setData({ showOfficialAccountTip });
      return;
    }
    
    // 如果本地没有存储，从服务器获取
    const userId = getApp().globalData.user_id;
    if (!userId) return;
    
    wx.request({
      url: getApp().globalData.wangz + '/button_setting/getButtonSetting',
      method: 'POST',
      header: {
        'content-type': 'application/json',
        'Authorization': wx.getStorageSync('token')
      },
      data: {
        user_id: userId,
        button_type: 'official_account'
      },
      success: (res) => {
        if (res.data && res.data.code === 200) {
          const showTip = res.data.data.button_status === '1';
          this.setData({ showOfficialAccountTip: showTip });
          wx.setStorageSync('showOfficialAccountTip', showTip);
        }
      },
      fail: (err) => {
        console.error('获取公众号提示状态失败:', err);
      }
    });
  },
  
  // 显示关闭确认弹窗
  showCloseConfirm() {
    this.setData({
      showCloseConfirmModal: true
    });
  },
  
  // 隐藏关闭确认弹窗
  hideCloseConfirm() {
    this.setData({
      showCloseConfirmModal: false
    });
  },
  
  // 确认关闭公众号提示
  confirmCloseOfficialAccountTip() {
    this.hideCloseConfirm(); // 先隐藏弹窗
    
    // 关闭提示
    this.setData({ showOfficialAccountTip: false });
    wx.setStorageSync('showOfficialAccountTip', false);
    
    // 同步到服务器
    const userId = getApp().globalData.user_id;
    if (!userId) return;
    
    wx.request({
      url: getApp().globalData.wangz + '/button_setting/saveButtonSetting',
      method: 'POST',
      header: {
        'content-type': 'application/json',
        'Authorization': wx.getStorageSync('token')
      },
      data: {
        user_id: userId,
        button_type: 'official_account',
        button_status: '0'
      },
      success: (res) => {
        console.log('公众号提示状态已更新');
      },
      fail: (err) => {
        console.error('更新公众号提示状态失败:', err);
      }
    });
  },
  
  // 点击关闭公众号提示 (保留但不再直接使用此方法)
  closeOfficialAccountTip() {
    this.setData({ showOfficialAccountTip: false });
    wx.setStorageSync('showOfficialAccountTip', false);
    
    // 同步到服务器
    const userId = getApp().globalData.user_id;
    if (!userId) return;
    
    wx.request({
      url: getApp().globalData.wangz + '/button_setting/saveButtonSetting',
      method: 'POST',
      header: {
        'content-type': 'application/json',
        'Authorization': wx.getStorageSync('token')
      },
      data: {
        user_id: userId,
        button_type: 'official_account',
        button_status: '0'
      },
      success: (res) => {
        console.log('公众号提示状态已更新');
      },
      fail: (err) => {
        console.error('更新公众号提示状态失败:', err);
      }
    });
  },

  // 显示服务号二维码弹窗
  showServiceQRCode() {
    this.setData({
      showServiceQRCode: true
    });
  },

  // 关闭服务号二维码弹窗
  closeServiceQRCode() {
    this.setData({
      showServiceQRCode: false
    });
  },

  // 阻止事件冒泡
  stopPropagation() {
    // 阻止事件冒泡
    return false;
  },
})