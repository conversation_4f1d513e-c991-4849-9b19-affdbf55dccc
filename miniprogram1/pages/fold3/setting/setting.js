// pages/fold3/setting/setting.js

Page({
  /**
   * 页面的初始数据
   */
  data: {
    isSubscribed: true, // 默认订阅状态
    showOfficialAccountTip: true, // 默认显示公众号提示
    showHotTopic: true, // 默认显示每日热榜
    showGroupNotice: true, // 默认显示微信群提示
    showTradingGroupNotice: true // 默认显示二手物品快速交易群提示
  },

  onClickLeft() {
    // 直接返回上一页，保持页面栈
    wx.navigateBack({
      delta: 1
    });
  },

  /**
   * 切换订阅状态
   */
  toggleSubscribe: function(e) {
    const isSubscribed = e.detail.value;
    this.setData({ isSubscribed });
    
    // 存储订阅状态到本地存储
    wx.setStorageSync('activitySubscribe', isSubscribed);
    
    // 获取用户ID
    const userId = getApp().globalData.user_id;
    
    // 同步到服务器
    wx.request({
      url: getApp().globalData.wangz + '/activity/saveSubscriptionStatus',
      method: 'POST',
      header: {
        'content-type': 'application/json',
        'Authorization': wx.getStorageSync('token')
      },
      data: {
        user_id: userId,
        subscribed: isSubscribed ? '1' : '0'
      },
      success: (res) => {
        console.log('订阅状态同步成功:', res.data);
      },
      fail: (err) => {
        console.error('订阅状态同步失败:', err);
        wx.showToast({
          title: '订阅状态同步失败',
          icon: 'none'
        });
      }
    });
    
    // 提示用户
    wx.showToast({
      title: isSubscribed ? '已开启活动通知' : '已关闭活动通知',
      icon: 'none'
    });
    
    // 更新全局活动提醒状态
    getApp().globalData.activitySubscribed = isSubscribed;
    
    // 如果取消订阅，清除活动红点提示
    if (!isSubscribed) {
      wx.removeTabBarBadge({
        index: 2 // 假设工具页在tabBar中的索引为2
      }).catch(() => {
        // 忽略可能的错误
      });
      // 清除本地存储的未读活动数
      wx.setStorageSync('unreadActivityCount', 0);
    } else {
      // 如果重新订阅，立即检查是否有新活动
      if (getApp().globalData.checkNewActivities) {
        getApp().globalData.checkNewActivities();
      }
    }
  },

  /**
   * 切换公众号提示状态
   */
  toggleOfficialAccountTip: function(e) {
    const showOfficialAccountTip = e.detail.value;
    this.setData({ showOfficialAccountTip });
    
    // 存储提示状态到本地存储
    wx.setStorageSync('showOfficialAccountTip', showOfficialAccountTip);
    
    // 获取用户ID
    const userId = getApp().globalData.user_id;
    
    // 同步到服务器
    wx.request({
      url: getApp().globalData.wangz + '/button_setting/saveButtonSetting',
      method: 'POST',
      header: {
        'content-type': 'application/json',
        'Authorization': wx.getStorageSync('token')
      },
      data: {
        user_id: userId,
        button_type: 'official_account',
        button_status: showOfficialAccountTip ? '1' : '0'
      },
      success: (res) => {
        console.log('公众号提示状态同步成功:', res.data);
      },
      fail: (err) => {
        console.error('公众号提示状态同步失败:', err);
        wx.showToast({
          title: '状态同步失败',
          icon: 'none'
        });
      }
    });
    
    // 提示用户
    wx.showToast({
      title: showOfficialAccountTip ? '已开启公众号提示' : '已关闭公众号提示',
      icon: 'none'
    });
    
    // 更新全局公众号提示状态
    getApp().globalData.showOfficialAccountTip = showOfficialAccountTip;
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function(options) {
    // 从本地存储读取订阅状态
    const isSubscribed = wx.getStorageSync('activitySubscribe');
    // 从本地存储读取公众号提示状态
    const showOfficialAccountTip = wx.getStorageSync('showOfficialAccountTip');
    const showHotTopic = wx.getStorageSync('showHotTopic');
    const showGroupNotice = wx.getStorageSync('showGroupNotice');
    const showTradingGroupNotice = wx.getStorageSync('showTradingGroupNotice');

    // 设置初始状态
    this.setData({
      isSubscribed: isSubscribed !== "" ? isSubscribed : true,
      showOfficialAccountTip: showOfficialAccountTip !== "" ? showOfficialAccountTip : true,
      showHotTopic: showHotTopic !== "" ? showHotTopic : true,
      showGroupNotice: showGroupNotice !== "" ? showGroupNotice : true,
      showTradingGroupNotice: showTradingGroupNotice !== "" ? showTradingGroupNotice : true
    });

    // 如果本地没有公众号提示状态，从服务器获取
    if (showOfficialAccountTip === "") {
      this.getOfficialAccountTipStatus();
    }
    if (showHotTopic === "") {
      this.getHotTopicSetting();
    }
    // 对于二手物品快速交易群，总是从服务器获取最新状态
    this.getTradingGroupNoticeSetting();
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function() {
    // 页面显示时重新获取最新的订阅状态
    const isSubscribed = wx.getStorageSync('activitySubscribe');
    const showOfficialAccountTip = wx.getStorageSync('showOfficialAccountTip');
    const showHotTopic = wx.getStorageSync('showHotTopic');
    const showGroupNotice = wx.getStorageSync('showGroupNotice');
    const showTradingGroupNotice = wx.getStorageSync('showTradingGroupNotice');

    if (isSubscribed !== "" || showOfficialAccountTip !== "") {
      this.setData({
        isSubscribed: isSubscribed !== "" ? isSubscribed : this.data.isSubscribed,
        showOfficialAccountTip: showOfficialAccountTip !== "" ? showOfficialAccountTip : this.data.showOfficialAccountTip
      });
    }
    if (showHotTopic !== "") {
      this.setData({
        showHotTopic: showHotTopic
      });
    }
    if (showGroupNotice !== "") {
      this.setData({ showGroupNotice });
    }
    if (showTradingGroupNotice !== "") {
      this.setData({ showTradingGroupNotice });
    }
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function() {

  },



  /**
   * 从服务器获取公众号提示状态
   */
  getOfficialAccountTipStatus: function() {
    const userId = getApp().globalData.user_id;
    if (!userId) return;
    
    wx.request({
      url: getApp().globalData.wangz + '/button_setting/getButtonSetting',
      method: 'POST',
      header: {
        'content-type': 'application/json',
        'Authorization': wx.getStorageSync('token')
      },
      data: {
        user_id: userId,
        button_type: 'official_account'
      },
      success: (res) => {
        if (res.data && res.data.code === 200) {
          const showTip = res.data.data.button_status === '1';
          this.setData({ showOfficialAccountTip: showTip });
          wx.setStorageSync('showOfficialAccountTip', showTip);
        }
      },
      fail: (err) => {
        console.error('获取公众号提示状态失败:', err);
      }
    });
  },

  // 新增每日热榜按钮逻辑
  getHotTopicSetting() {
    const userId = getApp().globalData.user_id;
    if (!userId) return;
    wx.request({
      url: getApp().globalData.wangz + '/button_setting/getButtonSetting',
      method: 'POST',
      header: {
        'content-type': 'application/json',
        'Authorization': wx.getStorageSync('token')
      },
      data: {
        user_id: userId,
        button_type: 'hot_topic'
      },
      success: (res) => {
        if (res.data && res.data.code === 200) {
          const show = res.data.data.button_status === '1';
          this.setData({ showHotTopic: show });
          wx.setStorageSync('showHotTopic', show);
        }
      }
    });
  },

  saveHotTopicSetting(status) {
    const userId = getApp().globalData.user_id;
    wx.request({
      url: getApp().globalData.wangz + '/button_setting/saveButtonSetting',
      method: 'POST',
      header: {
        'content-type': 'application/json',
        'Authorization': wx.getStorageSync('token')
      },
      data: {
        user_id: userId,
        button_type: 'hot_topic',
        button_status: status ? '1' : '0'
      },
      success: (res) => {
        if (res.data && res.data.code === 200) {
          this.setData({ showHotTopic: status });
          wx.setStorageSync('showHotTopic', status);
          wx.showToast({
            title: status ? '已开启每日热榜' : '已关闭每日热榜',
            icon: 'none'
          });
          
          // 立即更新所有页面的热榜显示状态
          const pages = getCurrentPages();
          for (let i = 0; i < pages.length; i++) {
            const page = pages[i];
            // 更新首页
            if (page.route && page.route.indexOf('fold1/home/<USER>') !== -1) {
              page.setData({ showHotTopic: status });
              // 如果首页定义了刷新热榜的方法，则调用
              if (typeof page.refreshHotTopic === 'function') {
                page.refreshHotTopic(status);
              }
            }
            
            // 确保全局变量也被更新
            getApp().globalData.showHotTopic = status;
          }
        }
      }
    });
  },

  onHotTopicSwitch(e) {
    this.saveHotTopicSetting(e.detail.value);
  },

  toggleGroupNotice: function(e) {
    const showGroupNotice = e.detail.value;
    this.setData({ showGroupNotice });
    wx.setStorageSync('showGroupNotice', showGroupNotice);
    const userId = getApp().globalData.user_id;
    wx.request({
      url: getApp().globalData.wangz + '/button_setting/saveButtonSetting',
      method: 'POST',
      header: {
        'content-type': 'application/json',
        'Authorization': wx.getStorageSync('token')
      },
      data: {
        user_id: userId,
        button_type: 'group_notice',
        button_status: showGroupNotice ? '1' : '0'
      },
      success: (res) => {
        wx.showToast({
          title: showGroupNotice ? '已开启微信群提示' : '已关闭微信群提示',
          icon: 'none'
        });
        // 通知首页或全局刷新群通知组件
        if (getApp().globalData.refreshGroupNotice) {
          getApp().globalData.refreshGroupNotice(showGroupNotice);
        }
      },
      fail: (err) => {
        wx.showToast({
          title: '状态同步失败',
          icon: 'none'
        });
      }
    });
  },

  // 获取二手物品快速交易群提示设置
  getTradingGroupNoticeSetting() {
    const userId = getApp().globalData.user_id;
    if (!userId) return;
    console.log('Setting - 获取二手物品快速交易群提示设置');
    wx.request({
      url: getApp().globalData.wangz + '/button_setting/getButtonSetting',
      method: 'POST',
      header: {
        'content-type': 'application/json',
        'Authorization': wx.getStorageSync('token')
      },
      data: {
        user_id: userId,
        button_type: 'trading_group_notice'
      },
      success: (res) => {
        console.log('Setting - 二手物品快速交易群提示设置响应:', res.data);
        if (res.data && res.data.code === 200) {
          const show = res.data.data.button_status === '1';
          console.log('Setting - 设置二手物品快速交易群提示状态:', show);
          this.setData({ showTradingGroupNotice: show });
          wx.setStorageSync('showTradingGroupNotice', show);
        }
      },
      fail: (err) => {
        console.error('Setting - 获取二手物品快速交易群提示设置失败:', err);
        // 如果请求失败，默认为开启状态
        this.setData({ showTradingGroupNotice: true });
        wx.setStorageSync('showTradingGroupNotice', true);
      }
    });
  },

  // 切换二手物品快速交易群提示
  toggleTradingGroupNotice: function(e) {
    const showTradingGroupNotice = e.detail.value;
    this.setData({ showTradingGroupNotice });
    wx.setStorageSync('showTradingGroupNotice', showTradingGroupNotice);
    const userId = getApp().globalData.user_id;
    wx.request({
      url: getApp().globalData.wangz + '/button_setting/saveButtonSetting',
      method: 'POST',
      header: {
        'content-type': 'application/json',
        'Authorization': wx.getStorageSync('token')
      },
      data: {
        user_id: userId,
        button_type: 'trading_group_notice',
        button_status: showTradingGroupNotice ? '1' : '0'
      },
      success: (res) => {
        wx.showToast({
          title: showTradingGroupNotice ? '已开启二手物品快速交易群提示' : '已关闭二手物品快速交易群提示',
          icon: 'none'
        });
      },
      fail: (err) => {
        wx.showToast({
          title: '状态同步失败',
          icon: 'none'
        });
      }
    });
  },
}) 