// pages/home/<USER>
import { setWatcher } from '../../../watch.js';
import eventBus from '../../../utils/eventBus';
// 引入角色管理器
import roleManager from '../../../utils/roleManager';

// 引入调试工具
const { debugLog } = require('../../../utils/debugUtil.js');
// 引入分享配置工具
const { getShareAppMessageConfig, getShareTimelineConfig } = require('../../../utils/shareConfig.js');
const app = getApp();

Page({
  data: {
    titlecolor: '0',
    unread_id: '',
    messages1: [], // 存储交易市场的消息数据
    messages2: [], // 存储告白倾诉的消息数据
    messages3: [], // 存储校园交易的消息数据
    gradeQueryIcon: '', // 成绩查询图标，动态设置
    leftColumnMessages2: [], // 左列数据
    rightColumnMessages2: [], // 右列数据
    messages4: [], // 存储提问求助的消息数据
    page1: 1, // 当前页码
    page2: 1,
    page3: 1,
    page4: 1,
    isLoading: false, // 是否正在加载数据
    refresherTriggered: false, // scroll-view下拉刷新状态
    swiperTransitioning: false, // swiper是否正在滑动中
    selectedItemId: 1,
    swiperIndex: 0, // swiper当前索引
    indicatorPosition: 0, // 横条指示器位置
    abc: [254, 251, 229],
    showGuideHint: false, // 是否显示引导提示
    guideStep: 1, // 当前引导步骤
    totalGuideSteps: 5, // 总引导步骤数
    guideClosing: false, // 引导是否正在关闭
    showFilterPopup: false, // 是否显示筛选弹窗
    // 为每个板块创建独立的筛选状态
    currentFilter1: 'all', // 树洞消息板块筛选条件
    currentFilter2: 'all', // 校园交易板块筛选条件
    currentFilter3: 'all', // 大学城树洞板块筛选条件
    currentFilter4: 'all', // 跑腿代办板块筛选条件
    currentFilterText1: '全部消息', // 树洞消息板块筛选显示文本
    currentFilterText2: '全部消息', // 校园交易板块筛选显示文本
    currentFilterText3: '全部消息', // 大学城树洞板块筛选显示文本
    currentFilterText4: '全部消息', // 跑腿代办板块筛选显示文本
    currentSchoolText: '请选择学校', // 当前选择的学校文本（用于filter按钮）
    navSchoolText: '请选择学校', // 导航栏显示的学校文本
    filterOptions: [ // 筛选选项
      { value: 'all', label: '全部消息', icon: '/images/pinglun.png', bgColor: '#ffedd5' },
      { value: '2', label: '发条说说', icon: '/images/dangshidati-01.png', bgColor: '#e0f2fe' },
      { value: '4', label: '告白倾诉', icon: '/images/收藏.png', bgColor: '#fff1f2' },
      { value: '99', label: '寻找搭子', icon: '/images/cansaitubiaozhuanqu-.png', bgColor: '#fef3c7' }
    ],
    grid: [
      { number: 1, text: "树洞消息", icon: "/images/pinglun.png" },
      { number: 2, text: "校园交易", icon: "/images/ershouwupin.png" },
      { number: 3, text: "大学城树洞", icon: "/images/xiaoyuan.png" },
      { number: 4, text: "跑腿代办", icon: "/images/paotuiren.png" }
    ],
    showUpdateNotice: false, // 是否显示更新公告
    noticeClosing: false, // 更新公告是否正在关闭
    updateNoticeAnimation: {}, // 在 data 中添加动画属性
    showPublishPopup: false, // 新增发布弹窗状态
    hotTopics: [],
    currentHotIndex: 0,
    hotTopicTimer: null,
    showHotTopicModal: false,
    showHotTopic: true,
    displayTopics: [], // 新增：用于存储当前显示的两条消息
    hasSchoolInfo: true, // 用户是否有学校信息
    showSchoolPrompt: false, // 是否显示学校选择提示
    showSideMenu: false, // 是否显示侧边菜单
    statusBarHeight: 0, // 状态栏高度
    userInfo: {}, // 用户信息
    gridListTop: 0, // grid-list的top位置
    swiperTop: 0, // swiper的top位置
    swiperBottom: 0, // swiper的bottom位置
    swiperHeight: 0, // swiper的高度
    showUniversalQuery: true, // 是否显示万能查询子菜单（默认展开）
    // 触摸相关变量
    touchStartX: 0,
    touchStartY: 0,
    touchStartTime: 0,
    // 侧边栏触摸相关变量
    sideMenuTouchStartX: 0,
    sideMenuTouchStartY: 0
  },

  onLoad(options) {
    // 获取状态栏高度
    const windowInfo = wx.getWindowInfo();
    this.setData({
      statusBarHeight: windowInfo.statusBarHeight
    });

    // 计算并设置布局位置
    this.calculateLayout();

    // 获取用户信息
    this.getUserInfo();

    // 初始化成绩查询图标
    this.initGradeQueryIcon();

    // 初始化watch配置
    this.setWatchConfig();

    setWatcher(this);
    this.getGridConfig();

    // 处理tab参数，如果有tab参数则切换到对应板块
    let initialTab = 1; // 默认第一个板块
    if (options && options.tab) {
      const tabId = parseInt(options.tab);
      if (tabId >= 1 && tabId <= 4) {
        initialTab = tabId;
      }
    }

    // 初始化横条指示器位置
    this.updateIndicatorPosition(initialTab - 1);
    this.setData({
      selectedItemId: initialTab,
      swiperIndex: initialTab - 1,
      page1: 1,
      page2: 1,
      page3: 1,
      page4: 1,
      page99: 1,
      messages1: [],
      messages2: [],
      leftColumnMessages2: [],
      rightColumnMessages2: [],
      messages3: [],
      messages4: [],
    });

    // 检查学校信息
    this.checkSchoolInfo();

    // 在检查学校信息后加载当前选中的板块
    this.loadMessages(() => {
      // 首页加载完成后，预加载其他板块
      this.preloadOtherTabs();
    });

    // 检查登录状态并处理引导
    this.checkLoginAndGuide();

    // 监听点赞状态变化
    eventBus.on('likeStatusChanged', this.handleLikeStatusChanged);
    eventBus.on('commentLikeStatusChanged', this.handleCommentLikeStatusChanged);

    // 获取热帖数据
    this.getHotTopics();

    // 启动热帖自动滚动
    this.startHotTopicScroll();

    const showHotTopic = wx.getStorageSync('showHotTopic');
    if (showHotTopic !== "") {
      this.setData({ showHotTopic });
    } else {
      this.getHotTopicSetting();
    }
  },

  // 将watch配置移到单独的方法中
  setWatchConfig() {
    this.watch = {
      selectedItemId: function(newVal) {
        // 不再清空所有数据，只是检查当前标签页是否需要加载数据
        this.checkAndLoadTabData(newVal);
      }
    };
  },

  viewMessageDetail: function(e) {
    let index, column, selectedMessage;

    // 检查是否是组件事件
    if (e.detail && e.detail.item) {
      // 来自组件的事件
      selectedMessage = e.detail.item;
      index = e.detail.index;
    } else {
      // 原有的直接点击事件
      index = e.currentTarget.dataset.index;
      column = e.currentTarget.dataset.column;

      if (this.data.selectedItemId === 2 && column) {
        // 瀑布流模式
        const columnKey = column === 'left' ? 'leftColumnMessages2' : 'rightColumnMessages2';
        selectedMessage = this.data[columnKey][index];
      } else {
        // 普通模式
        const selectedItemId = this.data.selectedItemId;
        selectedMessage = this.data[`messages${selectedItemId}`][index];
      }
    }

    // 构建URL，传递choose参数
    let url = `/packageEmoji/pages/messageDetail/messageDetail?id=${selectedMessage.id}`;

    // 根据当前选中的板块添加choose参数
    if (this.data.selectedItemId) {
      const chooseParam = this.data.selectedItemId === 2 ? 3 : this.data.selectedItemId; // 第二个标签使用choose=3
      url += `&choose=${chooseParam}`;
    }

    wx.navigateTo({
      url: url
    });
  },

  loadMessages(callback, filterType = null) {
    // 如果用户没有学校信息，不加载数据
    if (!this.data.hasSchoolInfo) {
      if (callback) callback();
      return;
    }

    if (this.data.isLoading && this.data[`page${this.data.selectedItemId}`] !== 1) {
      // 如果正在加载且不是第一页，则不重复加载
      return;
    }

    const { selectedItemId } = this.data;
    let pageKey = `page${selectedItemId}`;
    let messagesKey = `messages${selectedItemId}`;

    // 在第一次加载或刷新时显示loading
    if (this.data[pageKey] === 1 || this.data[messagesKey].length === 0) {
      this.setData({ isLoading: true });
    }

    // 构建请求数据
    let requestData = {
      choose: selectedItemId === 2 ? 3 : selectedItemId, // 第二个标签使用choose=3（校园交易）
      page: this.data[pageKey],
      user_id: wx.getStorageSync('user_id')
    };

    // 如果是树洞消息页面且有筛选条件，添加筛选参数
    const currentFilter = this.data[`currentFilter${selectedItemId}`] || 'all';
    if (selectedItemId === 1 && (filterType || currentFilter !== 'all')) {
      requestData.filter_choose = filterType || currentFilter;
    }

    wx.request({
      url: getApp().globalData.wangz + '/message/getMessages',
      method: 'POST',
      data: requestData,
      header: {
        'content-type': 'application/x-www-form-urlencoded'
      },
      success: (res) => {
        let newMessages = res.data.data || [];

        // 先立即关闭loading状态，提升用户体验
        this.setData({ isLoading: false });

        // 使用异步处理数据，避免阻塞UI
        setTimeout(() => {
          // 使用imageUtil统一处理所有图片
          const imageUtil = require('../../../utils/imageUtil.js');
          newMessages = imageUtil.processMessagesImages(newMessages);

          // 处理null值和数据类型转换
          newMessages = newMessages.map(item => {
            for (let key in item) {
              if (item[key] === null) {
                item[key] = '';
              }
            }

            // 确保ID和点赞数是数字类型
            if (item.id) {
              item.id = parseInt(item.id);
            }
            if (item.total_likes !== undefined) {
              item.total_likes = parseInt(item.total_likes) || 0;
            }

            return item;
          });

          // 如果是树洞消息页面，过滤掉flag=2的消息（低价急出）
          if (selectedItemId === 1) {
            newMessages = newMessages.filter(item => {
              const flag = parseInt(item.flag) || 0;
              return flag !== 2;
            });
          }

          const newPage = this.data[pageKey] + 1;

          // 如果是校园交易板块，需要特殊处理瀑布流数据
          if (selectedItemId === 2) {
            const processedMessages = this.processTradeMessages(newMessages);
            const allMessages = [...this.data[messagesKey], ...processedMessages];
            const { leftColumn, rightColumn } = this.distributeToColumns(allMessages);

            // 分批更新数据，先更新基础数据
            this.setData({
              [messagesKey]: allMessages,
              [pageKey]: newPage
            });

            // 再更新瀑布流数据
            setTimeout(() => {
              this.setData({
                leftColumnMessages2: leftColumn,
                rightColumnMessages2: rightColumn
              });
            }, 0);
          } else {
            this.setData({
              [messagesKey]: [...this.data[messagesKey], ...newMessages],
              [pageKey]: newPage
            });
          }

          if (callback) callback();
        }, 0);
      },
      fail: (error) => {
        this.setData({ isLoading: false });
        wx.showToast({
          title: '加载数据失败',
          icon: 'none'
        });

        if (callback) callback();
      },
    });
  },

  selectItem(event) {
    const itemId = event.currentTarget.dataset.number;
    const swiperIndex = itemId - 1; // 将number转换为swiper索引

    this.setData({
      selectedItemId: itemId,
      swiperIndex: swiperIndex
    });

    // 立即更新横条位置
    this.updateIndicatorPosition(swiperIndex);

    // 更新学校文本显示
    this.updateSchoolTextByTab();

    // 检查当前标签页是否有数据，没有则加载
    this.checkAndLoadTabData(itemId);
  },

  // swiper滑动事件
  onSwiperChange: function(e) {
    const current = e.detail.current;
    const selectedItemId = current + 1; // 将swiper索引转换为number

    this.setData({
      selectedItemId: selectedItemId,
      swiperIndex: current
    });

    // 立即更新横条位置到正确位置
    this.updateIndicatorPosition(current);

    // 更新学校文本显示
    this.updateSchoolTextByTab();

    // 检查当前标签页是否有数据，没有则加载
    this.checkAndLoadTabData(selectedItemId);
  },

  // swiper动画结束事件
  onSwiperAnimationFinish: function(e) {
    const current = e.detail.current;

    // 确保横条位置正确
    this.updateIndicatorPosition(current);

    // 滑动结束，恢复下拉刷新功能
    this.setData({
      swiperTransitioning: false
    });
  },

  // swiper实时滑动事件 - 在滑动期间禁用下拉刷新
  onSwiperTransition: function(e) {
    // 设置swiper正在滑动状态，禁用下拉刷新
    this.setData({
      swiperTransitioning: true
    });
  },

  // 更新横条指示器位置
  updateIndicatorPosition: function(index) {
    const position = this.calculateIndicatorPosition(index);
    this.setData({
      indicatorPosition: position
    });
  },

  // 计算横条指示器位置（对齐到文字中心）
  calculateIndicatorPosition: function(index) {
    const containerWidth = 700; // grid-list的宽度
    const itemWidth = containerWidth / 4; // 每个grid-item的宽度 175rpx
    const barWidth = 50; // 横条宽度

    // 每个grid-item的布局：图标(32rpx) + 间距(8rpx) + 文字
    const iconWidth = 32; // 图标宽度
    const gap = 8; // 图标和文字之间的间距

    // 计算每个item的起始位置
    const itemStart = index * itemWidth;

    // 计算item内容的中心位置（图标 + 间距 + 文字的整体中心）
    const itemCenter = itemStart + itemWidth / 2;

    // 向右偏移，让横条对齐到文字部分的中心
    // 偏移量 = (图标宽度 + 间距) / 2，这样横条会更靠近文字
    const textOffset = (iconWidth + gap) / 2;
    const textCenter = itemCenter + textOffset;

    // 横条位置 = 文字中心 - 横条宽度的一半
    const position = textCenter - barWidth / 2;

    return position;
  },

  scrollToTop() {

    // 如果用户没有学校信息，只回到顶部，不刷新数据
    if (!this.data.hasSchoolInfo) {
      wx.pageScrollTo({
        scrollTop: 0,
        duration: 300
      });
      return;
    }

    const currentTab = this.data.selectedItemId;
    const pageKey = `page${currentTab}`;
    const messagesKey = `messages${currentTab}`;

    // 显示loading状态
    this.setData({ isLoading: true });

    // 只清空当前标签页的数据
    const updateData = {
      [pageKey]: 1,
      [messagesKey]: []
    };

    // 如果是校园交易板块，还需要清空瀑布流数据
    if (currentTab === 2) {
      updateData.leftColumnMessages2 = [];
      updateData.rightColumnMessages2 = [];
    }

    this.setData(updateData);

    wx.pageScrollTo({
      scrollTop: 0,
      duration: 300
    });
    this.loadMessages();
  },

  publish() {
    if (roleManager.hasBasePermission()) {
      // 显示发布弹窗
      this.setData({
        showPublishPopup: true
      });
    } else {
      wx.showModal({
        title: '提示',
        content: '完成学生认证才能发帖',
        confirmText: '去认证',
        cancelText: '取消',
        success(res) {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/fold3/student/student'
            });
          }
        }
      });
    }
  },

  // 关闭发布弹窗
  closePublishPopup() {
    this.setData({
      showPublishPopup: false
    });
  },

  vxgroup() {
    wx.navigateTo({
      url: '/pages/foldshare/vxgroup/vxgroup'
    });
  },



  onShow() {
    // 重新计算布局，防止横竖屏切换导致的位置错误
    this.calculateLayout();

    // 检查是否需要重新打开侧边栏
    const shouldShowSideMenu = wx.getStorageSync('shouldShowSideMenu');
    const shouldShowUniversalQuery = wx.getStorageSync('shouldShowUniversalQuery');

    if (shouldShowSideMenu) {
      // 清除标记
      wx.removeStorageSync('shouldShowSideMenu');
      wx.removeStorageSync('shouldShowUniversalQuery');

      // 延迟打开侧边栏，确保页面完全加载
      setTimeout(() => {
        this.setData({
          showSideMenu: true,
          showUniversalQuery: shouldShowUniversalQuery || false
        });
        // 隐藏tabbar
        wx.hideTabBar({
          animation: false
        });
      }, 100);
    } else {
      // 如果不需要显示侧边栏，确保tabBar是显示的
      // 这里解决从其他页面返回后tabBar不显示的问题
      setTimeout(() => {
        wx.showTabBar({
          animation: false
        });
      }, 50);
    }

    // 重新检查学校信息（用户可能从学校选择页面返回）
    this.checkSchoolInfo();

    // 更新成绩查询图标（用户可能完成了认证）
    this.initGradeQueryIcon();

    // 重新获取用户信息，确保侧边栏显示最新状态
    this.getUserInfo();

    // 检查是否有新通知
    if (getApp().globalData.user_id) {
      getApp().globalData.updateAllNotifications();

      // 检查更新公告
      if (getApp().globalData.isLoggedIn) {
        this.checkUpdateNotice();
      }
    }

    // 重新获取热榜状态
    const showHotTopic = wx.getStorageSync('showHotTopic');
    if (showHotTopic !== "") {
      // 如果本地存储中的状态与当前状态不同，则更新
      if (this.data.showHotTopic !== showHotTopic) {
        this.refreshHotTopic(showHotTopic);
      }
    } else {
      // 如果本地没有存储，则使用全局配置
      const globalHotTopic = getApp().globalData.showHotTopic;
      if (this.data.showHotTopic !== globalHotTopic) {
        this.refreshHotTopic(globalHotTopic);
      }
    }

    // 切换到校园生活页时检查新活动
    getApp().globalData.checkNewActivities();
    // 更新所有通知
    getApp().globalData.updateAllNotifications();
    // 更新未读消息数Badge
    const app = getApp();
    if (app.globalData.websocket) {
      app.globalData.websocket.updateBadgeOnShow();
    }

    this.getGridConfig();
    this.updateSchoolText();
  },

  // 更新学校文本
  updateSchoolText() {
    try {
      const selectedSchool = wx.getStorageSync('selected_school');
      const hasSchoolInfo = wx.getStorageSync('has_school_info');

      if (!hasSchoolInfo || !selectedSchool) {
        // 用户没有选择学校
        this.setData({
          currentSchoolText: '请选择学校',
          navSchoolText: '请选择学校'
        });
        return;
      }

      // 导航栏始终显示学校简称（不含校区）
      this.setData({
        navSchoolText: selectedSchool.university_short_name || selectedSchool.short_name || '请选择学校'
      });

      // 同时更新侧边栏的用户信息
      this.getUserInfo();

      // 根据当前选中的标签页显示不同的文本
      this.updateSchoolTextByTab();
    } catch (error) {
      this.setData({
        currentSchoolText: '请选择学校',
        navSchoolText: '请选择学校'
      });
      // 同时更新侧边栏的用户信息
      this.getUserInfo();
    }
  },

  // 根据当前标签页更新学校文本
  updateSchoolTextByTab() {
    try {
      const selectedSchool = wx.getStorageSync('selected_school');
      const currentTab = this.data.selectedItemId;

      if (!selectedSchool) {
        this.setData({
          currentSchoolText: '请选择学校'
        });
        return;
      }

      let displayText = '';

      switch (currentTab) {
        case 1: // 树洞消息
        case 2: // 校园交易
        case 4: // 跑腿代办
          // 显示不带校区的学校简称
          displayText = selectedSchool.university_short_name || selectedSchool.short_name || '请选择学校';
          break;
        case 3: // 大学城树洞
          // 显示带校区的简称
          displayText = selectedSchool.short_name || '请选择学校';
          break;
        default:
          displayText = selectedSchool.university_short_name || selectedSchool.short_name || '请选择学校';
      }

      this.setData({
        currentSchoolText: displayText
      });
    } catch (error) {
      this.setData({
        currentSchoolText: '请选择学校'
      });
    }
  },

  // 处理交易消息数据
  processTradeMessages(messages) {
    return messages.map(item => {
      // 确保ID和点赞数是数字类型
      if (item.id) {
        item.id = parseInt(item.id);
      }
      if (item.total_likes !== undefined) {
        item.total_likes = parseInt(item.total_likes) || 0;
      }

      // 生成显示标题（取前20个字符，保留换行符）
      let displayTitle = '无标题';
      if (item.content) {
        let title = item.content.substring(0, 20);
        if (item.content.length > 20) {
          title += '...';
        }
        // 保留换行符，不替换为空格
        displayTitle = title;
      }

      // 确保images是数组并处理图片URL
      if (!item.images) {
        item.images = [];
      } else if (typeof item.images === 'string') {
        try {
          item.images = JSON.parse(item.images);
        } catch (e) {
          item.images = [];
        }
      }

      // 使用imageUtil统一处理所有图片
      const imageUtil = require('../../../utils/imageUtil.js');
      item = imageUtil.processMessageImages(item);

      // 如果没有图片，生成文字图片
      let textImageText = '';
      let textImageBg = '';

      if (!item.images || item.images.length === 0) {
        // 提取第一行或前20个字符作为文字图片内容
        const firstLine = item.content.split('\n')[0] || item.content;
        let text = firstLine.substring(0, 20);

        // 如果文字长度超过10个字符，在中间添加换行
        if (text.length > 10) {
          const midPoint = Math.floor(text.length / 2);
          // 找到最近的空格或标点符号作为换行点
          let breakPoint = midPoint;
          for (let i = midPoint; i < text.length && i < midPoint + 3; i++) {
            if (/[\s，。！？、]/.test(text[i])) {
              breakPoint = i;
              break;
            }
          }
          textImageText = text.substring(0, breakPoint) + '\n' + text.substring(breakPoint);
        } else {
          textImageText = text;
        }

        // 生成随机浅彩色背景
        textImageBg = this.generateRandomLightColor();
      }

      // 处理位置显示
      let displayWeizhi = '';
      if (item.weizhi) {
        if (item.weizhi === '沙河校区') {
          displayWeizhi = '沙河';
        } else if (item.weizhi === '学院路校区') {
          displayWeizhi = '学院路';
        } else {
          // 其他位置信息，最多显示前4个字
          displayWeizhi = item.weizhi.substring(0, 4);
        }
      }

      return {
        ...item,
        displayTitle,
        textImageText,
        textImageBg,
        displayWeizhi,
        flag: parseInt(item.flag) || 0  // 确保flag字段为数字类型
      };
    });
  },

  // 生成随机浅彩色
  generateRandomLightColor() {
    const colors = [
      '#FF6B9D', // 粉红色
      '#4ECDC4', // 青绿色
      '#45B7D1', // 蓝色
      '#96CEB4', // 薄荷绿
      '#FFEAA7', // 黄色
      '#DDA0DD', // 梅花色
      '#98D8C8', // 浅绿色
      '#F7DC6F', // 金黄色
      '#BB8FCE', // 紫色
      '#85C1E9', // 天蓝色
      '#F8C471', // 橙色
      '#82E0AA', // 绿色
      '#F1948A', // 珊瑚色
      '#AED6F1', // 浅蓝色
      '#D7BDE2'  // 淡紫色
    ];
    return colors[Math.floor(Math.random() * colors.length)];
  },

  // 将消息分配到左右两列
  distributeToColumns(messages) {
    const leftColumn = [];
    const rightColumn = [];

    messages.forEach((item, index) => {
      if (index % 2 === 0) {
        leftColumn.push(item);
      } else {
        rightColumn.push(item);
      }
    });

    return { leftColumn, rightColumn };
  },



  onHide() {
    // 如果侧边栏是打开的，关闭它并恢复tabbar
    if (this.data.showSideMenu) {
      this.setData({
        showSideMenu: false
      });
      wx.showTabBar({
        animation: false
      });
    }
  },

  onUnload() {
    if (this.fireworksTimer) {
      clearInterval(this.fireworksTimer);
    }
    if (this.checkLoginInterval) {
      clearInterval(this.checkLoginInterval);
    }

    // 清除横条重置定时器
    if (this.indicatorResetTimer) {
      clearTimeout(this.indicatorResetTimer);
    }

    // 清除触底加载防抖定时器
    if (this.loadingTimeout) {
      clearTimeout(this.loadingTimeout);
    }

    // 页面卸载时取消监听
    eventBus.off('likeStatusChanged', this.handleLikeStatusChanged);
    eventBus.off('commentLikeStatusChanged', this.handleCommentLikeStatusChanged);

    // 清除热帖定时器
    if (this.data.hotTopicTimer) {
      clearInterval(this.data.hotTopicTimer);
    }
  },

  onPullDownRefresh() {
    // 页面级下拉刷新已被scroll-view的下拉刷新替代
    // 直接停止页面级下拉刷新
    wx.stopPullDownRefresh();
  },

  onReachBottom() {
    // 页面级触底事件，现在由scroll-view的bindscrolltolower处理
  },

  // 新增：scroll-view触底事件处理
  onScrollToLower(e) {
    // 如果用户没有学校信息，不加载数据
    if (!this.data.hasSchoolInfo) {
      return;
    }

    // 防抖处理：如果正在加载，直接返回
    if (this.data.isLoading) {
      return;
    }

    const tab = e.currentTarget.dataset.tab;

    // 只有当前显示的tab才加载数据
    if (parseInt(tab) === this.data.selectedItemId) {
      // 防止重复触发，添加短暂延迟
      if (this.loadingTimeout) {
        clearTimeout(this.loadingTimeout);
      }

      this.loadingTimeout = setTimeout(() => {
        // 如果是树洞消息页面且有筛选条件，使用筛选加载
        const currentFilter = this.data[`currentFilter${this.data.selectedItemId}`] || 'all';
        if (this.data.selectedItemId === 1 && currentFilter !== 'all') {
          this.loadMoreFilteredMessages();
        } else {
          this.loadMessages();
        }
      }, 100); // 100ms防抖延迟
    }
  },

  // 新增：scroll-view下拉刷新事件处理
  onRefresherRefresh(e) {
    // 如果用户没有学校信息，不刷新数据
    if (!this.data.hasSchoolInfo) {
      // 关闭下拉刷新状态
      this.setData({
        refresherTriggered: false
      });
      return;
    }

    const tab = e.currentTarget.dataset.tabRefresh;

    // 只有当前显示的tab才刷新数据
    if (parseInt(tab) === this.data.selectedItemId) {
      this.setData({
        refresherTriggered: true,
        isLoading: true
      });

      const currentTab = this.data.selectedItemId;
      const pageKey = `page${currentTab}`;
      const messagesKey = `messages${currentTab}`;

      // 清空当前标签页的数据
      const updateData = {
        [pageKey]: 1,
        [messagesKey]: []
      };

      // 如果是校园交易板块，还需要清空瀑布流数据
      if (currentTab === 2) {
        updateData.leftColumnMessages2 = [];
        updateData.rightColumnMessages2 = [];
      }

      this.setData(updateData);

      // 加载数据
      const currentFilter = this.data[`currentFilter${currentTab}`] || 'all';
      if (currentTab === 1 && currentFilter !== 'all') {
        // 如果是树洞消息页面且有筛选条件，使用筛选加载
        this.loadMessagesWithFilter(currentFilter);
        // 刷新完成，关闭下拉刷新状态
        setTimeout(() => {
          this.setData({
            refresherTriggered: false
          });
        }, 500);
      } else {
        this.loadMessages(() => {
          // 刷新完成，关闭下拉刷新状态
          this.setData({
            refresherTriggered: false,
            isLoading: false
          });
        });
      }
    }
  },

  // 检查并加载标签页数据
  checkAndLoadTabData(tabId) {
    // 如果用户没有学校信息，不加载数据
    if (!this.data.hasSchoolInfo) {
      this.setData({ isLoading: false });
      return;
    }

    // 第三、四个标签不加载数据，显示预告页面
    if (tabId === 3 || tabId === 4) {
      this.setData({ isLoading: false });
      return;
    }

    const messagesKey = `messages${tabId}`;

    // 如果当前标签页没有数据，则加载
    if (this.data[messagesKey].length === 0) {
      // 显示loading状态
      this.setData({ isLoading: true });
      this.loadMessages();
    } else {
      // 确保loading状态关闭
      this.setData({ isLoading: false });
    }
  },

  // 预加载其他标签页的内容
  preloadOtherTabs() {
    // 如果用户没有学校信息，不预加载数据
    if (!this.data.hasSchoolInfo) {
      return;
    }

    const currentTab = this.data.selectedItemId;
    const allTabs = [1, 2]; // 只预加载有数据的标签页，第三、四个是预告页面

    // 过滤掉当前已加载的标签页
    const tabsToPreload = allTabs.filter(tab => tab !== currentTab);

    // 依次预加载其他标签页，每个间隔500ms避免并发请求过多
    tabsToPreload.forEach((tab, index) => {
      setTimeout(() => {
        this.preloadTabData(tab);
      }, (index + 1) * 500);
    });
  },

  // 预加载指定标签页的数据
  preloadTabData(tabId) {
    const pageKey = `page${tabId}`;
    const messagesKey = `messages${tabId}`;

    // 第三、四个标签是预告页面，不需要预加载
    if (tabId === 3 || tabId === 4) {
      return;
    }

    // 如果该标签页已有数据，则跳过
    if (this.data[messagesKey].length > 0) {
      return;
    }

    wx.request({
      url: getApp().globalData.wangz + '/message/getMessages',
      method: 'POST',
      data: {
        choose: tabId === 2 ? 3 : tabId, // 第二个标签使用choose=3（校园交易）
        page: 1,
        user_id: wx.getStorageSync('user_id')
      },
      header: {
        'content-type': 'application/x-www-form-urlencoded'
      },
      success: (res) => {
        let newMessages = res.data.data || [];

        // 处理消息数据
        // 使用imageUtil统一处理所有图片
        const imageUtil = require('../../../utils/imageUtil.js');
        newMessages = imageUtil.processMessagesImages(newMessages);

        // 处理null值和数据类型转换
        newMessages = newMessages.map(item => {
          for (let key in item) {
            if (item[key] === null) {
              item[key] = '';
            }
          }

          // 确保ID和点赞数是数字类型
          if (item.id) {
            item.id = parseInt(item.id);
          }
          if (item.total_likes !== undefined) {
            item.total_likes = parseInt(item.total_likes) || 0;
          }

          return item;
        });

        // 如果是校园交易板块，需要特殊处理瀑布流数据
        if (tabId === 2) {
          const processedMessages = this.processTradeMessages(newMessages);
          const { leftColumn, rightColumn } = this.distributeToColumns(processedMessages);

          this.setData({
            [messagesKey]: processedMessages,
            leftColumnMessages2: leftColumn,
            rightColumnMessages2: rightColumn,
            [pageKey]: 2 // 设置为2，表示下次加载第2页
          });
        } else {
          this.setData({
            [messagesKey]: newMessages,
            [pageKey]: 2 // 设置为2，表示下次加载第2页
          });
        }
      },
      fail: (error) => {
        // 预加载失败，静默处理
      }
    });
  },

  /**
   * 点赞状态变化处理（新的统一点赞组件）
   */
  onLikeChange: function(e) {
    const { targetId, isLiked, totalLikes } = e.detail
    // 处理组件事件，优先使用detail中的index
    const index = e.detail.index !== undefined ? e.detail.index : (e.currentTarget ? e.currentTarget.dataset.index : undefined)

    // 更新本地数据
    if (this.data.selectedItemId === 2) {
      // 瀑布流模式，需要更新原始数据和分列数据
      const messagesKey = `messages${this.data.selectedItemId}`;
      const messages = this.data[messagesKey];

      // 找到对应的消息并更新
      const targetMessage = messages.find(msg => msg.id === targetId);
      if (targetMessage) {
        targetMessage.is_liked = isLiked;
        targetMessage.total_likes = totalLikes;
      }

      // 重新分配到左右列
      const { leftColumn, rightColumn } = this.distributeToColumns(messages);

      this.setData({
        [messagesKey]: messages,
        leftColumnMessages2: leftColumn,
        rightColumnMessages2: rightColumn
      });
    } else {
      // 普通模式
      const messagesKey = `messages${this.data.selectedItemId}`;
      const messages = this.data[messagesKey];

      if (index !== undefined && messages[index] && messages[index].id === targetId) {
        messages[index].is_liked = isLiked;
        messages[index].total_likes = totalLikes;

        this.setData({
          [messagesKey]: messages
        });
      }
    }

    // 发送点赞状态更新事件
    const app = getApp();
    if (app.globalData.eventBus) {
      app.globalData.eventBus.emit('likeStatusChanged', {
        messageId: targetId,
        isLiked: isLiked,
        totalLikes: totalLikes
      });
    }
  },



  // 获取grid配置
  getGridConfig() {
    wx.request({
      url: getApp().globalData.wangz + '/message/getGridConfig',
      method: 'POST',
      data: {
        user_id: wx.getStorageSync('user_id')
      },
      header: {
        'content-type': 'application/x-www-form-urlencoded'
      },
      success: (res) => {
        if (res.data.status === 1) {
          // 保持固定的四个分类，不根据后端配置动态添加
          const fixedGrid = [
            { number: 1, text: "树洞消息", icon: "/images/pinglun.png" },
            { number: 2, text: "校园交易", icon: "/images/ershouwupin.png" },
            { number: 3, text: "大学城树洞", icon: "/images/xiaoyuan.png" },
            { number: 4, text: "跑腿代办", icon: "/images/paotuiren.png" }
          ];
          this.setData({
            grid: fixedGrid
          });
        }
      }
    });
  },

  // 检查登录状态并处理引导
  checkLoginAndGuide() {
    const app = getApp();
    // 检查是否已经登录
    if (app.globalData.isLogin) {
      this.checkGuideStatusAfterLogin();
    } else {
      // 设置轮询检查登录状态
      this.checkLoginInterval = setInterval(() => {
        const user_id = wx.getStorageSync('user_id');
        if (user_id) {
          clearInterval(this.checkLoginInterval);
          this.checkGuideStatusAfterLogin();
        }
      }, 1000); // 每秒检查一次

      // 设置超时，避免无限轮询
      setTimeout(() => {
        if (this.checkLoginInterval) {
          clearInterval(this.checkLoginInterval);
        }
      }, 10000); // 10秒后停止检查
    }
  },

  // 登录后检查引导状态
  checkGuideStatusAfterLogin() {
    const user_id = wx.getStorageSync('user_id');
    if (!user_id) {
      console.error('用户ID不存在');
      return;
    }


    // 先检查新手引导状态
    wx.request({
      url: getApp().globalData.wangz + '/guide_hint/getHintStatus',
      method: 'POST',
      data: {
        user_id: user_id,
        page_key: 'home_page',
        step_key: 'guide'
      },
      header: {
        'content-type': 'application/x-www-form-urlencoded'
      },
      success: (res) => {
        if (res.data.code === 1) {
          if (res.data.data && res.data.data.should_show_hint) {
            // 如果需要显示引导
            this.setData({
              showGuideHint: true,
              guideStep: 1,
              showUpdateNotice: false,
              noticeClosing: false,
              guideClosing: false
            });
          } else {
            // 如果引导已完成，检查更新公告
            this.checkUpdateNotice();
          }
        } else {
          console.error('引导状态检查返回错误:', res.data);
        }
      },
      fail: (error) => {
        console.error('引导状态检查请求失败:', error);
      }
    });
  },

  // 检查更新公告状态
  checkUpdateNotice() {
    const user_id = wx.getStorageSync('user_id');
    if (!user_id) {
      console.error('用户ID不存在');
      return;
    }
    
    wx.request({
      url: getApp().globalData.wangz + '/guide_hint/getHintStatus',
      method: 'POST',
      data: {
        user_id: user_id,
        page_key: 'home_page',
        step_key: 'notice'  // 改回正确的 step_key
      },
      header: {
        'content-type': 'application/x-www-form-urlencoded'
      },
      success: (res) => {
        if (res.data.code === 1 && res.data.data.should_show_hint) {
          this.setData({
            showUpdateNotice: true,
            showGuideHint: false,
            guideClosing: false,
            noticeClosing: false
          });
        }
      },
      fail: (error) => {
        console.error('更新公告检查失败:', error);
      }
    });
  },

  // 显示引导提示
  showGuide() {
    this.setData({
      showUpdateNotice: true,
      showGuideHint: false,
      guideClosing: false,
      noticeClosing: false
    });
  },

  // 下一步引导
  nextGuideStep() {
    const nextStep = this.data.guideStep + 1;
    this.setData({
      guideStep: nextStep
    });
  },

  // 跳过引导
  skipGuide() {
    const user_id = wx.getStorageSync('user_id');
    
    wx.request({
      url: getApp().globalData.wangz + '/guide_hint/updateHintStatus',
      method: 'POST',
      data: {
        user_id: user_id,
        page_key: 'home_page',
        step_key: 'guide'
      },
      header: {
        'content-type': 'application/x-www-form-urlencoded'
      },
      success: (res) => {
        if (res.data.code === 1) {
          this.setData({
            showGuideHint: false,
            guideClosing: false
          });
          
          // 检查更新公告状态
          wx.request({
            url: getApp().globalData.wangz + '/guide_hint/getHintStatus',
            method: 'POST',
            data: {
              user_id: user_id,
              page_key: 'home_page',
              step_key: 'notice'
            },
            header: {
              'content-type': 'application/x-www-form-urlencoded'
            },
            success: (noticeRes) => {
              if (noticeRes.data.code === 1 && noticeRes.data.data.should_show_hint) {
                setTimeout(() => {
                  this.setData({
                    showUpdateNotice: true
                  });
                }, 500); // 等待引导关闭动画完成后再显示更新公告
              }
            }
          });
        }
      }
    });
  },

  // 关闭引导提示
  closeGuideHint() {
    const user_id = wx.getStorageSync('user_id');
    
    this.setData({
      guideClosing: true
    });

    wx.request({
      url: getApp().globalData.wangz + '/guide_hint/updateHintStatus',
      method: 'POST',
      data: {
        user_id: user_id,
        page_key: 'home_page',
        step_key: 'guide'
      },
      header: {
        'content-type': 'application/x-www-form-urlencoded'
      },
      success: (res) => {
        if (res.data.code === 1) {
          // 检查更新公告状态
          wx.request({
            url: getApp().globalData.wangz + '/guide_hint/getHintStatus',
            method: 'POST',
            data: {
              user_id: user_id,
              page_key: 'home_page',
              step_key: 'notice'
            },
            header: {
              'content-type': 'application/x-www-form-urlencoded'
            },
            success: (noticeRes) => {
              this.setData({
                showGuideHint: false,
                guideClosing: false
              });
              
              // 如果需要显示更新公告
              if (noticeRes.data.code === 1 && noticeRes.data.data.should_show_hint) {
                setTimeout(() => {
                  this.setData({
                    showUpdateNotice: true
                  });
                }, 500); // 等待引导关闭动画完成后再显示更新公告
              } else {
                this.setData({
                  showUpdateNotice: false
                });
              }
            },
            fail: () => {
              this.setData({
                showGuideHint: false,
                guideClosing: false,
                showUpdateNotice: false
              });
            }
          });
        }
      },
      fail: () => {
        this.setData({
          showGuideHint: false,
          guideClosing: false,
          showUpdateNotice: false
        });
      }
    });
  },

  // 关闭更新公告
  closeUpdateNotice() {
    const user_id = wx.getStorageSync('user_id');
    if (!user_id) {
      console.error('用户ID不存在');
      return;
    }
    
    this.setData({
      noticeClosing: true
    });

    wx.request({
      url: getApp().globalData.wangz + '/guide_hint/updateHintStatus',
      method: 'POST',
      data: {
        user_id: user_id,
        page_key: 'home_page',
        step_key: 'notice'  // 改回正确的 step_key
      },
      header: {
        'content-type': 'application/x-www-form-urlencoded'
      },
      success: (res) => {
        if (res.data.code === 1) {
          this.setData({
            showUpdateNotice: false,
            noticeClosing: false
          });
        } else {
          console.error('更新公告状态更新失败:', res.data);
          // 即使更新失败也关闭弹窗
          this.setData({
            showUpdateNotice: false,
            noticeClosing: false
          });
        }
      },
      fail: (error) => {
        console.error('更新公告状态更新请求失败:', error);
        this.setData({
          showUpdateNotice: false,
          noticeClosing: false
        });
      }
    });
  },





  // 处理点赞状态变化
  handleLikeStatusChanged: function(data) {
    const { messageId, isLiked, totalLikes } = data;
    const messagesKey = `messages${this.data.selectedItemId}`;
    const messages = this.data[messagesKey];
    if (!messages) return;
    
    const updatedMessages = messages.map(message => {
      if (message.id === messageId) {
        return {
          ...message,
          is_liked: isLiked,
          total_likes: totalLikes
        };
      }
      return message;
    });
    
    this.setData({
      [messagesKey]: updatedMessages
    });
  },

  // 处理评论点赞状态变化
  handleCommentLikeStatusChanged: function(data) {
    // 如果需要更新评论的点赞状态，在这里实现
  },

  goToGongju() {
    this.closeUpdateNotice(); // 先关闭更新公告
    wx.switchTab({
      url: '/pages/fold4/gongju/gongju',
      success: () => {
        // 跳转成功
      },
      fail: (error) => {
        console.error('跳转失败:', error);
        wx.navigateTo({
          url: '/pages/fold4/gongju/gongju',
          fail: (err) => {
            console.error('navigateTo也失败:', err);
            wx.showToast({
              title: '跳转失败，请重试',
              icon: 'none'
            });
          }
        });
      }
    });
  },

  // 添加热门按钮点击事件处理函数
  goToHot() {
    wx.navigateTo({
      url: '/pages/fold1/hot/hot'
    });
  },

  // 获取热帖数据
  getHotTopics() {
    wx.request({
      url: getApp().globalData.wangz + '/message/getHotMessages',
      method: 'POST',
      data: {
        user_id: wx.getStorageSync('user_id')
      },
      header: {
        'content-type': 'application/x-www-form-urlencoded'
      },
      success: (res) => {
        if (res.data.code === 1) {
          const hotTopics = res.data.data.map(item => item.content || '');

          // 使用相同的处理函数来处理初始显示
          const processTopic = (topic, index) => {
            // 严格获取第一行内容，遇到任何换行符都截断
            const firstLine = topic.split(/[\n\r\u2028\u2029]/)[0].trim();
            // 移除所有可能的换行符和空白字符
            const cleanContent = firstLine.replace(/[\n\r\u2028\u2029]/g, '').trim();

            // 如果原文本包含换行符，直接截断不显示后续内容
            const hasLineBreak = /[\n\r\u2028\u2029]/.test(topic);

            if (hasLineBreak) {
              // 有换行符时，直接使用第一行内容，不管长度
              return {
                content: cleanContent,
                rank: index + 1
              };
            } else {
              // 没有换行符时，按原逻辑限制30个字符
              return {
                content: cleanContent.length > 30 ? cleanContent.substring(0, 30) + '...' : cleanContent,
                rank: index + 1
              };
            }
          };

          // 初始显示第1和第2条，设置currentHotIndex为0，但第一次滚动时直接跳到下一组
          const initialDisplayTopics = hotTopics.slice(0, 2).map((topic, index) =>
            processTopic(topic, index)
          );

          this.setData({
            hotTopics: hotTopics,
            currentHotIndex: 0, // 从0开始
            displayTopics: initialDisplayTopics
          });
        }
      }
    });
  },

  // 启动热帖自动滚动
  startHotTopicScroll() {
    if (this.data.hotTopicTimer) {
      clearInterval(this.data.hotTopicTimer);
    }

    const timer = setInterval(() => {
      if (!this.data.hotTopics || this.data.hotTopics.length === 0) {
        return;
      }

      // 直接移动到下一个位置
      const nextIndex = (this.data.currentHotIndex + 1) % this.data.hotTopics.length;
      const followingIndex = (nextIndex + 1) % this.data.hotTopics.length;

      // 获取要显示的两条热榜内容
      const firstTopic = this.data.hotTopics[nextIndex] || '';
      const secondTopic = this.data.hotTopics[followingIndex] || '';

      // 处理换行符，只显示第一行
      const processTopic = (topic, actualIndex) => {
        // 严格获取第一行内容，遇到任何换行符都截断
        const firstLine = topic.split(/[\n\r\u2028\u2029]/)[0].trim();
        // 移除所有可能的换行符和空白字符
        const cleanContent = firstLine.replace(/[\n\r\u2028\u2029]/g, '').trim();

        // 如果原文本包含换行符，直接截断不显示后续内容
        const hasLineBreak = /[\n\r\u2028\u2029]/.test(topic);

        if (hasLineBreak) {
          // 有换行符时，直接使用第一行内容，不管长度
          return {
            content: cleanContent,
            rank: actualIndex + 1
          };
        } else {
          // 没有换行符时，按原逻辑限制30个字符
          return {
            content: cleanContent.length > 30 ? cleanContent.substring(0, 30) + '...' : cleanContent,
            rank: actualIndex + 1
          };
        }
      };

      // 更新显示的两条消息
      const displayTopics = [
        processTopic(firstTopic, nextIndex),
        processTopic(secondTopic, followingIndex)
      ];

      this.setData({
        currentHotIndex: nextIndex,
        displayTopics: displayTopics
      });
    }, 3000); // 每3秒滚动一次

    this.setData({ hotTopicTimer: timer });
  },

  showHotTopicModal(e) {
    if (e && e.stopPropagation) {
      e.stopPropagation();
    }
    this.setData({
      showHotTopicModal: true
    });
  },

  closeHotTopicModal() {
    this.setData({
      showHotTopicModal: false
    });
  },

  stopPropagation(e) {
    if (e && e.stopPropagation) {
      e.stopPropagation();
    }
  },

  getHotTopicSetting() {
    const userId = getApp().globalData.user_id;
    if (!userId) {
      this.setData({ showHotTopic: true }); // 默认显示
      return;
    }
    
    wx.request({
      url: getApp().globalData.wangz + '/button_setting/getButtonSetting',
      method: 'POST',
      header: {
        'content-type': 'application/json',
        'Authorization': wx.getStorageSync('token')
      },
      data: {
        user_id: userId,
        button_type: 'hot_topic'
      },
      success: (res) => {
        if (res.data && res.data.code === 200) {
          this.setData({ showHotTopic: res.data.data.button_status === '1' });
          wx.setStorageSync('showHotTopic', res.data.data.button_status === '1');
        }
      }
    });
  },
  
  // 添加刷新热榜状态的方法
  refreshHotTopic(status) {
    // 如果状态未定义，从本地存储获取
    if (status === undefined) {
      status = wx.getStorageSync('showHotTopic');
    }
    
    // 更新显示状态
    this.setData({ showHotTopic: status });
    
    // 如果是关闭热榜，同时关闭热榜弹窗
    if (status === false && this.data.showHotTopicModal) {
      this.closeHotTopicModal();
    }
    
    // 如果是开启热榜但没有热榜数据，重新获取数据
    if (status === true && (!this.data.hotTopics || this.data.hotTopics.length === 0)) {
      this.getHotTopics();
    }
  },

  // 定位按钮点击事件
  onLocationTap() {
    wx.navigateTo({
      url: '/pages/school-select/school-select'
    });
  },

  // 筛选按钮点击事件
  onFilterTap() {
    const currentTab = this.data.selectedItemId;

    // 只有在树洞消息页面才显示筛选弹窗
    if (currentTab === 1) {
      this.setData({
        showFilterPopup: true
      });
    } else {
      const tabNames = {
        2: '校园交易',
        3: '大学城树洞',
        4: '跑腿代办'
      };

      wx.showToast({
        title: `${tabNames[currentTab]}筛选功能`,
        icon: 'none',
        duration: 2000
      });
    }


  },

  // 筛选弹窗关闭事件
  onFilterClose() {
    this.setData({
      showFilterPopup: false
    });
  },

  // 筛选选项选择事件
  onFilterSelect(e) {
    const filter = e.detail.value;
    const currentTab = this.data.selectedItemId;
    const filterTexts = {
      'all': '全部消息',
      '2': '发条说说',
      '4': '告白倾诉',
      '99': '寻找搭子'
    };

    // 更新当前板块的筛选状态
    const updateData = {
      [`currentFilter${currentTab}`]: filter,
      [`currentFilterText${currentTab}`]: filterTexts[filter],
      showFilterPopup: false
    };

    this.setData(updateData);

    // 重新加载消息数据
    this.loadFilteredMessages(filter);
  },

  // 加载筛选后的消息
  loadFilteredMessages(filter) {
    this.setData({
      messages1: [],
      page1: 1,
      isLoading: true
    });

    // 根据筛选条件获取消息
    this.loadMessagesWithFilter(filter);
  },

  // 根据筛选条件加载消息
  loadMessagesWithFilter(filter) {
    // 如果用户没有学校信息，不加载数据
    if (!this.data.hasSchoolInfo) {
      return;
    }

    const chooseValue = filter === 'all' ? 1 : filter; // 全部消息用choose=1，其他用对应的choose值

    wx.request({
      url: getApp().globalData.wangz + '/message/getMessages',
      method: 'POST',
      data: {
        choose: chooseValue,
        page: this.data.page1,
        user_id: wx.getStorageSync('user_id')
      },
      header: {
        'content-type': 'application/x-www-form-urlencoded'
      },
      success: (res) => {
        let newMessages = res.data.data || [];

        // 使用imageUtil统一处理所有图片
        const imageUtil = require('../../../utils/imageUtil.js');
        newMessages = imageUtil.processMessagesImages(newMessages);

        // 处理null值和数据类型转换
        newMessages = newMessages.map(item => {
          for (let key in item) {
            if (item[key] === null) {
              item[key] = '';
            }
          }

          // 确保ID和点赞数是数字类型
          if (item.id) {
            item.id = parseInt(item.id);
          }
          if (item.total_likes !== undefined) {
            item.total_likes = parseInt(item.total_likes) || 0;
          }

          return item;
        });

        this.setData({
          messages1: newMessages,
          page1: this.data.page1 + 1,
          isLoading: false
        });
      },
      fail: (error) => {
        console.error('加载筛选消息失败:', error);
        this.setData({
          isLoading: false
        });
        wx.showToast({
          title: '加载失败，请重试',
          icon: 'none'
        });
      }
    });
  },

  // 加载更多筛选消息（分页）
  loadMoreFilteredMessages() {
    if (this.data.isLoading) {
      return;
    }

    this.setData({ isLoading: true });

    const currentFilter = this.data[`currentFilter${this.data.selectedItemId}`] || 'all';
    const chooseValue = currentFilter === 'all' ? 1 : currentFilter;

    wx.request({
      url: getApp().globalData.wangz + '/message/getMessages',
      method: 'POST',
      data: {
        choose: chooseValue,
        page: this.data.page1,
        user_id: wx.getStorageSync('user_id')
      },
      header: {
        'content-type': 'application/x-www-form-urlencoded'
      },
      success: (res) => {
        let newMessages = res.data.data || [];

        // 先立即关闭loading状态，提升用户体验
        this.setData({ isLoading: false });

        // 使用异步处理数据，避免阻塞UI
        setTimeout(() => {
          // 使用imageUtil统一处理所有图片
          const imageUtil = require('../../../utils/imageUtil.js');
          newMessages = imageUtil.processMessagesImages(newMessages);

          // 处理null值和数据类型转换
          newMessages = newMessages.map(item => {
            for (let key in item) {
              if (item[key] === null) {
                item[key] = '';
              }
            }

            // 确保ID和点赞数是数字类型
            if (item.id) {
              item.id = parseInt(item.id);
            }
            if (item.total_likes !== undefined) {
              item.total_likes = parseInt(item.total_likes) || 0;
            }

            return item;
          });

          this.setData({
            messages1: [...this.data.messages1, ...newMessages],
            page1: this.data.page1 + 1
          });
        }, 0);
      },
      fail: (error) => {
        console.error('加载更多筛选消息失败:', error);
        this.setData({
          isLoading: false
        });
      }
    });
  },

  // 检查学校信息
  checkSchoolInfo() {
    const hasSchoolInfo = wx.getStorageSync('has_school_info');
    const selectedSchool = wx.getStorageSync('selected_school');
    const userId = getApp().globalData.user_id;
    const schoolId = wx.getStorageSync('school_id');



    const previousHasSchoolInfo = this.data.hasSchoolInfo;

    // 检查用户是否有学校信息
    const userHasSchoolInfo = hasSchoolInfo === true && selectedSchool;

    if (!userHasSchoolInfo) {
      // 用户没有学校信息，显示选择提示
      this.setData({
        hasSchoolInfo: false,
        showSchoolPrompt: true
      });
    } else {
      // 用户有学校信息，隐藏选择提示
      this.setData({
        hasSchoolInfo: true,
        showSchoolPrompt: false
      });

      // 如果之前没有学校信息，现在有了，需要重新加载数据
      if (!previousHasSchoolInfo) {
        this.reloadCurrentTabData();
      }
    }

    // 更新学校文本显示
    this.updateSchoolText();
  },

  // 重新加载当前标签页数据
  reloadCurrentTabData() {
    const currentTab = this.data.selectedItemId;
    const pageKey = `page${currentTab}`;
    const messagesKey = `messages${currentTab}`;

    // 重置页码和数据
    const updateData = {
      [pageKey]: 1,
      [messagesKey]: []
    };

    // 如果是校园交易板块，还需要清空瀑布流数据
    if (currentTab === 2) {
      updateData.leftColumnMessages2 = [];
      updateData.rightColumnMessages2 = [];
    }

    this.setData(updateData);

    // 重新加载数据
    this.loadMessages();
  },

  // 跳转到学校选择页面
  goToSchoolSelect() {
    this.closeSideMenu();
    wx.navigateTo({
      url: '/pages/school-select/school-select'
    });
  },

  // 处理用户信息区域点击（当需要选择学校时）
  handleUserInfoClick() {
    if (this.data.userInfo.needSelectSchool) {
      this.goToSchoolSelect();
    }
  },

  // 切换万能查询展开状态
  toggleUniversalQuery() {
    this.setData({
      showUniversalQuery: !this.data.showUniversalQuery
    });
  },

  // 跳转到校车时刻表
  goToSchoolBus() {
    // 保存侧边栏状态和万能查询展开状态
    wx.setStorageSync('shouldShowSideMenu', true);
    wx.setStorageSync('shouldShowUniversalQuery', true);
    this.closeSideMenu();
    wx.navigateTo({
      url: '/pages/fold3/xiaoche/xiaoche'
    });
  },

  // 预览校历图片
  goToSchoolCalendar() {
    const imageUrl = getApp().globalData.wangz + '/uploads/校历.png';
    wx.previewImage({
      current: imageUrl,
      urls: [imageUrl]
    });
  },

  // 预览系号表图片
  goToDepartmentTable() {
    const imageUrl = getApp().globalData.wangz + '/uploads/系号表.png';
    wx.previewImage({
      current: imageUrl,
      urls: [imageUrl]
    });
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: '北京最棒的大学城树洞社区 - 鲸语校园',
      path: '/pages/fold1/home/<USER>',
      imageUrl: getApp().globalData.wangz + '/uploads/jingyu.png',
      success: function(res) {
        console.log('分享成功');
        wx.showToast({
          title: '分享成功',
          icon: 'success',
          duration: 1500
        });
      },
      fail: function(res) {
        console.log('分享失败');
      }
    };
  },

  /**
   * 用户点击右上角分享到朋友圈
   */
  onShareTimeline() {
    return {
      title: '北京最棒的大学城树洞社区 - 鲸语校园',
      query: '',
      imageUrl: getApp().globalData.wangz + '/uploads/jingyu.png'
    };
  },

  // 初始化成绩查询图标
  initGradeQueryIcon() {
    try {
      // 检查是否有认证学校信息
      const verifiedSchoolInfo = wx.getStorageSync('verified_school_info');

      if (verifiedSchoolInfo && verifiedSchoolInfo.logo_url) {
        // 有认证学校，使用认证学校的logo
        this.setData({
          gradeQueryIcon: verifiedSchoolInfo.logo_url
        });
      } else {
        // 没有认证学校，使用全局默认学校图标
        const defaultIcon = getApp().globalData.wangz + '/uploads/jingyu.png';
        this.setData({
          gradeQueryIcon: defaultIcon
        });
      }
    } catch (error) {
      console.error('初始化成绩查询图标失败:', error);
      // 出错时也使用全局默认学校图标
      const defaultIcon = getApp().globalData.wangz + '/uploads/jingyu.png';
      this.setData({
        gradeQueryIcon: defaultIcon
      });
    }
  },

  // 跳转到成绩查询
  goToGradeQuery() {
    try {
      // 直接跳转到成绩查询页面
      wx.navigateTo({
        url: '/pages/grade/grade'
      });
    } catch (error) {
      console.error('跳转成绩查询失败:', error);
      wx.showToast({
        title: '跳转失败，请重试',
        icon: 'none'
      });
    }
  },





  // 获取用户信息
  getUserInfo() {
    try {
      const userInfo = wx.getStorageSync('userInfo') || {};
      const selectedSchool = wx.getStorageSync('selected_school') || {};
      const hasSchoolInfo = wx.getStorageSync('has_school_info');

      // 检查是否有学校信息
      if (!hasSchoolInfo || !selectedSchool.id) {
        // 没有学校信息，显示请选择学校，使用通用默认头像
        const defaultAvatar = getApp().globalData.wangz + '/uploads/jingyu.png';
        this.setData({
          userInfo: {
            username: '请选择学校',
            avatar: defaultAvatar,
            school: '点击选择您的学校',
            needSelectSchool: true // 标记需要选择学校
          }
        });
        return;
      }

      // 获取学校头像，优先使用学校logo_url，否则使用默认头像
      let schoolAvatar = '/images/beihang.png';
      if (selectedSchool.logo_url) {
        schoolAvatar = selectedSchool.logo_url;
      }

      // 上方显示校区简称，下方显示学校全名（不带校区）
      const schoolShortName = selectedSchool.short_name || '北航学子'; // 如"北航(沙河)"
      const universityName = selectedSchool.university_name || '北京航空航天大学'; // 如"北京航空航天大学"

      this.setData({
        userInfo: {
          username: schoolShortName, // 上方显示校区简称
          avatar: schoolAvatar,
          school: universityName, // 下方显示学校全名（不带校区）
          needSelectSchool: false // 已有学校信息
        }
      });

      // 更新成绩查询图标
      this.initGradeQueryIcon();
    } catch (error) {
      console.error('获取用户信息失败:', error);
      // 出错时也显示请选择学校，使用通用默认头像
      const defaultAvatar = getApp().globalData.wangz + '/uploads/jingyu.png';
      this.setData({
        userInfo: {
          username: '请选择学校',
          avatar: defaultAvatar,
          school: '点击选择您的学校',
          needSelectSchool: true
        }
      });
    }
  },

  // 切换侧边菜单显示状态
  toggleSideMenu() {
    const newShowState = !this.data.showSideMenu;
    this.setData({
      showSideMenu: newShowState
    });

    // 动态隐藏/显示tabbar
    if (newShowState) {
      // 稍微延迟执行，确保状态更新完成
      setTimeout(() => {
        wx.hideTabBar({
          animation: false
        });
      }, 50);
    } else {
      setTimeout(() => {
        wx.showTabBar({
          animation: false
        });
      }, 50);
    }
  },

  // 关闭侧边菜单
  closeSideMenu() {
    this.setData({
      showSideMenu: false,
      showUniversalQuery: false // 关闭侧边栏时也收起万能查询子菜单
    });

    // 显示tabbar
    wx.showTabBar({
      animation: false
    });
  },



  // 跳转到个人资料
  goToProfile() {
    // 保存侧边栏状态
    wx.setStorageSync('shouldShowSideMenu', true);
    this.closeSideMenu();
    wx.navigateTo({
      url: '/pages/fold3/geren/geren'
    });
  },

  // 跳转到设置页面
  goToSettings() {
    // 保存侧边栏状态
    wx.setStorageSync('shouldShowSideMenu', true);
    this.closeSideMenu();
    wx.navigateTo({
      url: '/pages/fold3/setting/setting'
    });
  },

  // 跳转到关于我们页面
  goToAbout() {
    // 保存侧边栏状态
    wx.setStorageSync('shouldShowSideMenu', true);
    this.closeSideMenu();
    wx.navigateTo({
      url: '/pages/fold3/guanyu/guanyu'
    });
  },

  // 跳转到更新公告页面
  goToUpdateNotice() {
    // 保存侧边栏状态
    wx.setStorageSync('shouldShowSideMenu', true);
    this.closeSideMenu();
    wx.navigateTo({
      url: '/pages/fold3/gengxin/gengxin'
    });
  },

  // 触摸开始事件
  onTouchStart(e) {
    // 只在树洞消息板块处理左划手势
    if (this.data.selectedItemId !== 1) return;

    this.setData({
      touchStartX: e.touches[0].clientX,
      touchStartY: e.touches[0].clientY,
      touchStartTime: Date.now()
    });
  },

  // 触摸移动事件
  onTouchMove(e) {
    // 只在树洞消息板块处理左划手势
    if (this.data.selectedItemId !== 1) return;

    const currentX = e.touches[0].clientX;
    const currentY = e.touches[0].clientY;
    const deltaX = currentX - this.data.touchStartX;
    const deltaY = currentY - this.data.touchStartY;

    // 判断是否为左划手势（向右滑动）
    // 滑动距离要求：需要滑动超过70px，且垂直偏移小于80px
    if (deltaX > 70 && Math.abs(deltaY) < 80) {
      // 防止重复触发
      if (!this.data.showSideMenu) {
        this.toggleSideMenu();
      }
    }
  },

  // 触摸结束事件
  onTouchEnd(e) {
    // 只在树洞消息板块处理左划手势
    if (this.data.selectedItemId !== 1) return;

    // 重置触摸数据
    this.setData({
      touchStartX: 0,
      touchStartY: 0,
      touchStartTime: 0
    });
  },

  // 侧边栏触摸开始事件
  onSideMenuTouchStart(e) {
    this.setData({
      sideMenuTouchStartX: e.touches[0].clientX,
      sideMenuTouchStartY: e.touches[0].clientY
    });
  },

  // 侧边栏触摸移动事件
  onSideMenuTouchMove(e) {
    const currentX = e.touches[0].clientX;
    const currentY = e.touches[0].clientY;
    const deltaX = currentX - this.data.sideMenuTouchStartX;
    const deltaY = currentY - this.data.sideMenuTouchStartY;

    // 判断是否为右划手势（向左滑动关闭侧边栏）
    // 滑动距离要求：需要向左滑动超过80px，且垂直偏移小于100px
    if (deltaX < -80 && Math.abs(deltaY) < 100) {
      // 关闭侧边栏
      this.closeSideMenu();
    }
  },

  // 侧边栏触摸结束事件
  onSideMenuTouchEnd(e) {
    // 重置触摸数据
    this.setData({
      sideMenuTouchStartX: 0,
      sideMenuTouchStartY: 0
    });
  },

  /**
   * 计算并设置布局位置
   * 解决grid-list和swiper的定位问题
   */
  calculateLayout() {
    const statusBarHeight = this.data.statusBarHeight || 0;
    const navBarHeight = 44; // 导航栏高度（固定44px）
    const gridListHeight = 140; // grid-list高度（rpx转px约为70px）
    const gridListMargin = 15; // grid-list的间距（rpx转px约为7.5px）

    // 获取系统信息
    const systemInfo = wx.getSystemInfoSync();
    const screenHeight = systemInfo.screenHeight;
    const windowHeight = systemInfo.windowHeight;
    const safeAreaBottom = systemInfo.safeArea ? systemInfo.safeArea.bottom : windowHeight;

    // 计算tabBar高度，使用多种方法确保准确性
    let tabBarHeight = screenHeight - windowHeight;

    // 如果计算出的tabBar高度异常，使用默认值
    if (tabBarHeight <= 0 || tabBarHeight > 100) {
      // 根据设备类型设置默认tabBar高度
      if (systemInfo.model && systemInfo.model.includes('iPhone')) {
        // iPhone设备，考虑底部安全区域
        const bottomSafeArea = screenHeight - safeAreaBottom;
        tabBarHeight = Math.max(50 + bottomSafeArea, 80); // tabBar + 底部安全区域
      } else {
        tabBarHeight = 50; // Android等其他设备默认50px
      }
    }

    // 计算grid-list的top位置
    // 位置 = 状态栏高度 + 导航栏高度 + 间距
    const gridListTop = statusBarHeight + navBarHeight + Math.round(gridListMargin / 2);

    // 计算swiper的top位置
    // 位置 = grid-list的top + grid-list高度 + 额外间距
    const swiperTop = gridListTop + Math.round(gridListHeight / 2) + Math.round(gridListMargin / 2);

    // 计算swiper的bottom位置，避免被tabBar遮挡，增加额外的安全边距
    const swiperBottom = Math.max(tabBarHeight + 10, 60); // 增加10px安全边距，最小60px

    // 计算swiper的高度：屏幕高度 - swiper顶部位置 - tabBar高度 - 安全边距
    const swiperHeight = Math.max(screenHeight - swiperTop - tabBarHeight - 10, 200); // 最小200px高度

    console.log('布局计算:', {
      statusBarHeight,
      navBarHeight,
      gridListTop,
      swiperTop,
      swiperBottom,
      swiperHeight,
      tabBarHeight,
      screenHeight,
      windowHeight,
      safeAreaBottom,
      deviceModel: systemInfo.model,
      bottomSafeArea: screenHeight - safeAreaBottom
    });

    this.setData({
      gridListTop,
      swiperTop,
      swiperBottom,
      swiperHeight
    });
  },



  /**
   * 页面尺寸变化时重新计算布局
   */
  onResize() {
    // 获取最新的窗口信息
    const windowInfo = wx.getWindowInfo();
    this.setData({
      statusBarHeight: windowInfo.statusBarHeight
    });

    // 重新计算布局
    this.calculateLayout();
  }
});