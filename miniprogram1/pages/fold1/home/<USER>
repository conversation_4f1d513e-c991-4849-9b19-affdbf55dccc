/**
 * Z-Index 层级管理说明：
 * 1: 内容区域 (swiper)
 * 999: 固定按钮
 * 1000: 导航栏
 * 1001: 顶部标签栏 (grid-list)
 * 9999: 弹窗和遮罩
 * 10000+: 引导和特殊弹窗
 */

/* 顶部标签样式 */
.grid-list{
  display: flex;
  flex-direction: column;
  background-color: white;
  border-radius: 25rpx;
  position: fixed;
  top: 0; /* 使用top而不是margin-top，通过JS动态设置 */
  left: 27rpx;
  width: 700rpx;
  height: 140rpx; /* 增加高度容纳按钮 */
  z-index: 1001; /* 确保在导航栏之上 */
  transition: top 0.2s ease; /* 添加平滑过渡 */
}

/* 标签页区域 */
.grid-tabs {
  display: flex;
  flex-wrap: wrap;
  position: relative;
  height: 80rpx;
}

/* 横条指示器样式 */
.indicator-bar {
  position: absolute;
  bottom: 8rpx;
  left: 0;
  width: 50rpx;
  height: 4rpx;
  background: linear-gradient(90deg, rgb(231, 112, 0), rgb(255, 140, 0));
  border-radius: 2rpx;
  transition: transform 0.3s ease;
  z-index: 1000;
}
.grid-item{
  width: 25%;
  height: 80rpx;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  gap: 8rpx;
}

.grid-item image{
  width: 32rpx;
  height: 32rpx;
}
.grid-item text{
  font-size: 24rpx;
  margin-top: 0;
  font-family: Georgia, 'Times New Roman', Times, serif;
}

/* 定位和筛选按钮行 */
.filter-buttons-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 60rpx;
  padding: 0 20rpx; /* 减少左右padding，让按钮有更多空间 */
  gap: 20rpx; /* 在两个按钮之间添加间距 */
}

/* 单个按钮样式 */
.filter-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6rpx;
  padding: 6rpx 16rpx;
  border-radius: 12rpx;
  background-color: transparent;
  transition: all 0.2s ease;
  flex: 1; /* 让按钮占据一半空间 */
  min-width: 80rpx;
}

.filter-button:active {
  background-color: #e9ecef;
  transform: scale(0.95);
}

/* 按钮图标 */
.filter-icon {
  width: 30rpx;
  height: 30rpx;
}

/* 按钮文字 */
.filter-text {
  font-size: 24rpx;
  color: #666;
  font-weight: 500;
}

/* 筛选按钮特殊样式 */
.filter-button.filter-select {
  justify-content: center;
  gap: 6rpx; /* 文字和图标之间的间距 */
  min-width: 120rpx;
  padding: 6rpx 12rpx;
  flex: 1; /* 确保筛选按钮也占据一半空间 */
}

/* 筛选按钮右侧箭头 */
.filter-arrow {
  width: 30rpx;
  height: 30rpx;
}

/* 预告页面样式 */
.preview-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  padding: 40rpx;
  margin-top: 10rpx;
}

.preview-content {
  text-align: center;
}

.preview-title {
  display: block;
  font-size: 80rpx;
  font-family: 'STKaiti', '楷体', 'KaiTi', serif;
  color: #666;
  margin-bottom: 30rpx;
  font-weight: normal;
  line-height: 1.2;
}

.preview-subtitle {
  display: block;
  font-size: 32rpx;
  font-family: 'STKaiti', '楷体', 'KaiTi', serif;
  color: #999;
  font-weight: normal;
}


/* 背景颜色 */
.gradient-background {
  min-height: 100vh;
  height: auto;
  background: linear-gradient(to bottom, rgb(245, 239, 227),white);
  width: 100%;
  overflow-x: hidden;
  position: relative;
}
/* 保留num-item2样式，可能在其他地方使用 */
.num-item2 {
  box-sizing: border-box;
  border-radius: 34rpx;
  margin: 35rpx;
  padding: 20rpx 25rpx 15rpx 25rpx;
  display: flex;
  flex-direction: column;
  width: 90%;
  background: rgba(255, 255, 255, 0.8);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  min-height: auto;
  height: auto !important;
  position: relative;
  z-index: 1;
}

/* 导航栏 */
.navsetting{
  --nav-bar-background-color: rgb(245, 239, 227);
  z-index: 9999;  /* 添加最高层级的z-index */
  position: relative;  /* 确保z-index生效 */
}

.navtext{
  margin-bottom: 10rpx;
  font-family: '北航-刀隶体 Regular';
  font-size: 62rpx;
  margin-left: 15rpx;
}

.custom-icon2{
  width: 60rpx;
 height: 60rpx;
 margin-right: 10rpx;
}
/* 右侧按钮组 */
.fixed-button {
  position: fixed;
  bottom: 20rpx;
  right: 20rpx;
  color: white;
  background-color: rgba(255, 255, 255,0.5);
  padding: 10rpx;
  border-radius: 50%;
  width: 60rpx; 
  height: 60rpx;
  z-index: 999; /* 添加z-index确保按钮显示在最上层 */
}
.custom-icon{
  border-radius: 50%;
  width: 60rpx;
  height: 60rpx;
  display: block;
}
.fixed-button2 {
  position: fixed;
  bottom: 125rpx;
  right: 20rpx;
  color: white;
  background-color:rgba(255, 255, 255,0.5);
  padding: 10rpx;
  border-radius: 50%;
  width: 60rpx; 
  height: 60rpx;
  z-index: 999;
  transition: all 0.3s ease;
}
.fixed-button3 {
  position: fixed;
  bottom: 230rpx;
  right: 20rpx;
  color: white;
  background-color:rgba(255, 255, 255,0.5);
  padding: 10rpx;
  border-radius: 50%;
  width: 60rpx; 
  height: 60rpx;
  z-index: 999;
  transition: all 0.3s ease;
}
.fixed-button4 {
  position: fixed;
  bottom: 335rpx;
  right: 20rpx;
  color: white;
  background-color:rgba(255, 255, 255,0.5);
  padding: 10rpx;
  border-radius: 50%;
  width: 60rpx; 
  height: 60rpx;
  z-index: 999;
  transition: all 0.3s ease;
}
.fixed-button4.active {
  transform: scale(1.2);
  background-color: rgba(255, 107, 107, 0.8);
  animation: pulse 1s infinite;
}

.fixed-button5 {
  position: fixed;
  bottom: 440rpx;
  right: 20rpx;
  color: white;
  background-color:rgba(255, 255, 255,0.5);
  padding: 10rpx;
  border-radius: 50%;
  width: 60rpx; 
  height: 60rpx;
  z-index: 999;
  transition: all 0.3s ease;
  pointer-events: auto;
}
.fixed-button5.active {
  transform: scale(1.2);
  background-color: rgba(255, 107, 107, 0.8);
  animation: pulse 1s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(255, 107, 107, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10rpx rgba(255, 107, 107, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(255, 107, 107, 0);
  }
}
/* 搜索框设置 */
.srch{
  margin-right:150rpx;
  margin-bottom: 16rpx;
  width: 400rpx;
}


/* Swiper样式 */
.content-swiper {
  width: 100%;
  position: fixed;
  left: 0;
  right: 0;
  z-index: 1; /* 确保在最底层 */
  /* top和bottom通过内联样式动态设置 */
}

.swiper-content {
  height: 100%;
  width: 100%;
  -webkit-overflow-scrolling: touch; /* iOS 惯性滚动 */
}
/* 保留热榜中的touxiang1样式覆盖 */
.hot-topic-item .touxiang1 {
  margin-bottom: 0;
}

/* touxian样式已移至全局app.wxss */

/* 投票样式已移至组件中 */

/* 投票相关样式已移至组件中 */

/* 更新公告动画相关样式 */
.update-notice-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  display: flex;
  justify-content: center;
  align-items: center;
  visibility: visible;
  pointer-events: none;
}

.update-mask-background {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  z-index: 9998;
  opacity: 0;
  transition: opacity 0.3s ease-in-out;
  pointer-events: none;
}

.update-show .update-mask-background {
  opacity: 1;
  pointer-events: auto;
}

.update-hide .update-mask-background {
  opacity: 0;
  pointer-events: none;
}

.update-notice-box {
  width: 600rpx;
  background: linear-gradient(135deg, #FFFFFF, #FFF5F8);
  padding: 40rpx 0;
  border-radius: 30rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 10000;
  opacity: 0;
  transform: scale(0.1);
  transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  pointer-events: none;
  max-height: 85vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  transform-origin: center;
}

.update-show .update-notice-box {
  opacity: 1;
  transform: scale(1);
  pointer-events: auto;
}

.update-hide .update-notice-box {
  opacity: 0;
  transform: scale(0.1);
  pointer-events: none;
}

/* 更新公告内容样式 */
.update-notice-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  padding-bottom: 15rpx;
  margin-bottom: 15rpx;
  border-bottom: 2rpx solid rgba(255, 105, 180, 0.1);
  width: calc(100% - 50rpx);
  margin-left: auto;
  margin-right: auto;
}

.update-notice-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 15rpx;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(255, 105, 180, 0.2);
}

.update-notice-body {
  flex: 0 0 auto;
  margin: 20rpx 0;
  padding: 25rpx;
  position: relative;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 20rpx;
  border: 2rpx solid rgba(255, 105, 180, 0.1);
  width: calc(100% - 50rpx);
  box-sizing: border-box;
  margin-left: auto;
  margin-right: auto;
}

.update-notice-box .update-text {
  white-space: pre-line;
  line-height: 1.8;
  color: #333;
  font-size: 28rpx;
  display: block;
  padding: 0;
  width: 100%;
  word-break: break-word;
  text-align: left;
  margin: 0;
}

.update-notice-box .update-title {
  font-size: 32rpx;
  font-weight: 600;
  background: linear-gradient(135deg, #FF69B4, #FF8C98);
  -webkit-background-clip: text;
  color: transparent;
  margin-bottom: 8rpx;
  text-align: center;
}

.update-notice-box .update-version {
  font-size: 24rpx;
  color: #FF69B4;
  margin-bottom: 6rpx;
  text-align: center;
  opacity: 0.9;
}

.update-notice-box .update-date {
  font-size: 22rpx;
  color: #999;
  text-align: center;
  margin-bottom: 0;
}

.update-notice-footer {
  padding-top: 25rpx;
  margin-top: auto;
  text-align: center;
  border-top: 2rpx solid rgba(255, 105, 180, 0.1);
  width: 100%;
}

.update-btn-group {
  display: flex;
  justify-content: center;
  gap: 20rpx;
  width: 90%;
  margin: 0 auto;
  box-sizing: border-box;
}

.update-btn-later {
  background: #f5f5f5 !important;
  color: #666 !important;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.05) !important;
  flex: 1;
  font-size: 28rpx !important;
  height: 88rpx !important;
  line-height: 88rpx !important;
  border-radius: 44rpx !important;
  transition: all 0.3s ease !important;
  border: none !important;
  font-weight: 500 !important;
  max-width: 220rpx !important;
  text-align: center !important;
}

.update-btn-check {
  background: linear-gradient(135deg, #FF69B4, #FF8C98) !important;
  color: white !important;
  flex: 1;
  font-size: 28rpx !important;
  height: 88rpx !important;
  line-height: 88rpx !important;
  border-radius: 44rpx !important;
  transition: all 0.3s ease !important;
  box-shadow: 0 6rpx 16rpx rgba(255, 105, 180, 0.3) !important;
  border: none !important;
  position: relative;
  overflow: hidden;
  font-weight: 500 !important;
  max-width: 220rpx !important;
  text-align: center !important;
}

.update-btn-check::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(
    to bottom right,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0) 40%,
    rgba(255, 255, 255, 0.4) 50%,
    rgba(255, 255, 255, 0) 60%,
    rgba(255, 255, 255, 0) 100%
  );
  transform: rotate(45deg);
  animation: shineEffect 3s infinite;
}

.update-btn-later:active {
  transform: scale(0.98);
  background: #f0f0f0 !important;
}

.update-btn-check:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 8rpx rgba(255, 105, 180, 0.2) !important;
}

@keyframes shineEffect {
  0% {
    left: -50%;
  }
  100% {
    left: 150%;
  }
}

/* 烟花动画样式 */
.fireworks-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 9998;
}

.firework {
  position: absolute;
  bottom: 0;
  width: 3rpx;
  height: 3rpx;
  border-radius: 50%;
  animation: launch 1.2s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

.particle {
  position: absolute;
  width: 3rpx;
  height: 15rpx;
  border-radius: 50%;
  transform-origin: center center;
  opacity: 0;
  box-shadow: 0 0 6rpx 1rpx currentColor;
}

.particle-branch {
  position: absolute;
  width: 100%;
  height: 100%;
  transform-origin: center;
}

@keyframes launch {
  0% {
    transform: translateY(0) scale(1);
    opacity: 1;
  }
  50% {
    transform: translateY(-45vh) scale(1);
    opacity: 1;
  }
  55% {
    transform: translateY(-50vh) scale(1.2);
    opacity: 0;
  }
  100% {
    transform: translateY(-50vh) scale(1);
    opacity: 0;
  }
}

@keyframes explode {
  0% {
    transform: rotate(var(--angle)) translateY(0) scale(1);
    opacity: 0;
  }
  5% {
    opacity: 1;
    transform: rotate(var(--angle)) translateY(5rpx) scale(1);
  }
  35% {
    opacity: 0.9;
    transform: rotate(var(--angle)) translateY(var(--distance)) scale(1);
  }
  70% {
    opacity: 0.4;
    transform: rotate(var(--angle)) translateY(calc(var(--distance) * 1.2)) scale(0.8);
  }
  100% {
    opacity: 0;
    transform: rotate(var(--angle)) translateY(calc(var(--distance) * 1.5)) scale(0);
  }
}

@keyframes sparkle {
  0% {
    opacity: 0;
    transform: scale(1) rotate(0deg);
  }
  10% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
  100% {
    opacity: 0;
    transform: scale(0.5) rotate(180deg);
  }
}

.gradient-text2 {
  background: linear-gradient(to right, #b829db, #213ddd);
  -webkit-background-clip: text;
  color: transparent;
}

/* 引导提示样式 */
.guide-hint-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  display: flex;
  justify-content: center;
  align-items: center;
  visibility: visible;
  pointer-events: none;
}

.mask-background {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  z-index: 9998;
  opacity: 0;
  transition: opacity 0.3s ease-in-out;
  pointer-events: none;
}

.guide-show .mask-background {
  opacity: 1;
  pointer-events: auto;
}

.guide-hide .mask-background {
  opacity: 0;
  pointer-events: none;
}

.guide-hint-content {
  width: 500rpx;
  background: linear-gradient(135deg, #FFFFFF, #F9FAFF);
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 8rpx 24rpx rgba(149, 157, 165, 0.1);
  position: relative;
  z-index: 10000;
  transition: all 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  transform-origin: center;
  opacity: 1;
  overflow: hidden;
}

.guide-show {
  pointer-events: auto;
}

.guide-hide {
  pointer-events: none;
}

.guide-hint-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

.guide-hint-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 20rpx;
}

.guide-hint-title {
  font-size: 40rpx;
  font-weight: bold;
  background: linear-gradient(135deg, #FF69B4, #FFB6C1);
  -webkit-background-clip: text;
  color: transparent;
  margin-bottom: 10rpx;
}

.guide-hint-body {
  padding: 20rpx 0;
}

.guide-text {
  display: block;
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
  text-align: left;
  padding: 10rpx;
}

.guide-hint-footer {
  margin-top: 20rpx;
  text-align: center;
}

.guide-hint-btn {
  background: linear-gradient(135deg, #FF69B4, #FFB6C1);
  color: white;
  border: none;
  padding: 12rpx 40rpx;
  border-radius: 30rpx;
  font-size: 26rpx;
  box-shadow: 0 4rpx 12rpx rgba(255, 182, 193, 0.3);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  min-width: 180rpx;
  max-width: 300rpx;
  margin: 0 auto;
}

.guide-hint-btn.gradient-btn {
  background: linear-gradient(135deg, #FFB6C1, #FFD700);
}

/* 修改高亮区域样式 */
.highlight-nav {
  position: fixed;
  bottom: 230rpx;
  right: 120rpx;
  width: 500rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16rpx;
  padding: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  z-index: 10000;
}

.highlight-content {
  position: fixed;
  bottom: 335rpx;
  right: 120rpx;
  width: 500rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16rpx;
  padding: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  z-index: 10000;
}

.highlight-publish {
  position: fixed;
  bottom: 125rpx;
  right: 120rpx;
  width: 500rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16rpx;
  padding: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  z-index: 10000;
}

.highlight-message {
  position: fixed;
  top: 223rpx;
  left: 0;
  width: 100%;
  bottom: 0;
  background: transparent;  /* 移除背景色，因为已经由mask-background提供 */
  z-index: 10000;
}

.highlight-message .guide-hint-body {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 500rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16rpx;
  padding: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.highlight-message .guide-hint-footer {
  position: fixed;
  top: calc(50% + 80rpx);
  left: 50%;
  transform: translateX(-50%);
  width: 500rpx;
  text-align: center;
}

/* 移除之前的before伪元素 */
.highlight-message::before {
  display: none;
}

/* 按钮脉冲动画 */
.pulse {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

/* 引导提示动画 */
.guide-show .guide-hint-content {
  opacity: 1;
  transform: scale(1);
  animation: slideIn 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.guide-hide .guide-hint-content {
  opacity: 0;
  transform: scale(0.8);
  animation: slideOut 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

@keyframes slideIn {
  0% {
    opacity: 0;
    transform: scale(0.8) translateY(60rpx);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@keyframes slideOut {
  0% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
  100% {
    opacity: 0;
    transform: scale(0.8) translateY(60rpx);
  }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-20rpx);
  }
  60% {
    transform: translateY(-10rpx);
  }
}

/* 引导激活状态 */
.guide-active {
  z-index: 10001 !important;
  background-color: rgba(255, 255, 255, 0.95);
  box-shadow: 0 0 20rpx rgba(255, 182, 193, 0.5);
}

/* 第五步时的特殊处理 */
.guide-hint-mask.step-5 {
  pointer-events: none;
}

.guide-hint-mask.step-5 .mask-background {
  background: rgba(0, 0, 0, 0.4);
  pointer-events: auto;
}

/* 第五步的高亮区域 */
.highlight-message {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: transparent;
  z-index: 10000;
  pointer-events: auto;
}

/* 第五步的提示文本容器 */
.highlight-message .guide-hint-body {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 500rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16rpx;
  padding: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  z-index: 10001;
}

.highlight-message .guide-hint-footer {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, 80rpx);
  width: 500rpx;
  text-align: center;
  z-index: 10001;
}

/* 第五步跳过按钮位置调整 */
.highlight-message .skip-guide {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  transform: none;
}

/* 保持标签栏可点击 */
.guide-hint-mask.step-5 .grid-list.guide-active {
  pointer-events: auto;
}

.guide-hint-mask.step-5 .grid-list.guide-active .grid-item {
  pointer-events: auto;
}

/* 修改第五步遮罩层样式 */
.guide-hint-mask.step-5 .mask-background {
  background: rgba(0, 0, 0, 0.4);
  pointer-events: auto;
}

.grid-list.guide-active {
  z-index: 10001 !important;
  background-color: rgba(255, 255, 255, 0.95) !important;
  box-shadow: 0 0 20rpx rgba(255, 182, 193, 0.5);
}

/* 引导激活状态 */
.guide-active {
  z-index: 10001 !important;
  background-color: rgba(255, 255, 255, 0.95);
  box-shadow: 0 0 20rpx rgba(255, 182, 193, 0.5);
}

/* 第五步时的特殊处理 */
.guide-hint-mask.step-5 {
  pointer-events: none;
}

.guide-hint-mask.step-5 .mask-background {
  background: rgba(0, 0, 0, 0.4);
  pointer-events: auto;
}

/* 第五步的高亮区域 */
.highlight-message {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: transparent;
  z-index: 10000;
  pointer-events: auto;
}

/* 第五步的提示文本容器 */
.highlight-message .guide-hint-body {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 500rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16rpx;
  padding: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  z-index: 10001;
}

.highlight-message .guide-hint-footer {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, 80rpx);
  width: 500rpx;
  text-align: center;
  z-index: 10001;
}

/* 第五步跳过按钮位置调整 */
.highlight-message .skip-guide {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  transform: none;
}

/* 保持标签栏可点击 */
.guide-hint-mask.step-5 .grid-list.guide-active {
  pointer-events: auto;
}

.guide-hint-mask.step-5 .grid-list.guide-active .grid-item {
  pointer-events: auto;
}

/* 跳过引导按钮基础样式 */
.skip-guide {
  position: absolute;
  font-size: 24rpx;
  color: #999;
  padding: 8rpx 20rpx;
  border-radius: 30rpx;
  background: rgba(255, 255, 255, 0.8);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  z-index: 10001;
}

/* 第一步的跳过按钮位置 */
.guide-hint-content .skip-guide {
  top: 20rpx;
  right: 20rpx;
}

/* 第二步到第四步的跳过按钮位置 */
.highlight-publish .skip-guide,
.highlight-nav .skip-guide,
.highlight-content .skip-guide {
  bottom: -60rpx;
  right: 20rpx;
  top: auto;
}

/* 第五步的跳过按钮位置 */
.highlight-message .skip-guide {
  bottom: 40%;
  right: 40rpx;
  top: auto;
}

.notice-body {
  max-height: 400rpx;
  overflow-y: auto;
  padding: 20rpx;
  margin: 10rpx 0;
}

.notice-text {
  white-space: pre-wrap;
  word-break: break-word;
  font-size: 28rpx;
  line-height: 1.6;
  display: block;
}

/* 修改滚动条样式 */
.notice-body::-webkit-scrollbar {
  width: 4rpx;
  background-color: transparent;
}

.notice-body::-webkit-scrollbar-thumb {
  background-color: #ddd;
  border-radius: 4rpx;
}

.update-btn-group {
  display: flex;
  justify-content: space-between;
  gap: 20rpx;
  width: 100%;
  padding: 0 30rpx;
  box-sizing: border-box;
}

.update-btn-later {
  background: #f5f5f5 !important;
  color: #666 !important;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.05) !important;
  flex: 1;
  font-size: 28rpx !important;
  height: 88rpx !important;
  line-height: 88rpx !important;
  border-radius: 44rpx !important;
  transition: all 0.3s ease !important;
  border: none !important;
  font-weight: 500 !important;
  width: 50% !important;
  text-align: center !important;
}

.update-btn-check {
  background: linear-gradient(135deg, #FF69B4, #FF8C98) !important;
  color: white !important;
  flex: 1;
  font-size: 28rpx !important;
  height: 88rpx !important;
  line-height: 88rpx !important;
  border-radius: 44rpx !important;
  transition: all 0.3s ease !important;
  box-shadow: 0 6rpx 16rpx rgba(255, 105, 180, 0.3) !important;
  border: none !important;
  position: relative;
  overflow: hidden;
  font-weight: 500 !important;
  width: 50% !important;
  text-align: center !important;
}

.update-btn-check::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(
    to bottom right,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0) 40%,
    rgba(255, 255, 255, 0.4) 50%,
    rgba(255, 255, 255, 0) 60%,
    rgba(255, 255, 255, 0) 100%
  );
  transform: rotate(45deg);
  animation: shineEffect 3s infinite;
}

.update-btn-later:active {
  transform: scale(0.98);
  background: #f0f0f0 !important;
}

.update-btn-check:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 8rpx rgba(255, 105, 180, 0.2) !important;
}

@keyframes shineEffect {
  0% {
    left: -50%;
  }
  100% {
    left: 150%;
  }
}

/* 每日热帖样式 */
.hot-topic-container {
  width: 100%;
  padding: 0;
  box-sizing: border-box;
}

.hot-topic-item {
  background-color: rgba(254, 251, 229, 0.8);
  border-radius: 34rpx;
  padding: 20rpx 25rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  margin: 10rpx 35rpx 35rpx 35rpx;
  width: 90%;
  box-sizing: border-box;
  position: relative;
  min-height: 120rpx;
  display: flex;
  flex-direction: column;
}

.hot-topic-header {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
  height: 70rpx;
}

.hot-topic-avatar {
  width: 70rpx;
  height: 70rpx;
  border-radius: 15%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: none;
  margin-bottom: 0;
  overflow: hidden;
}

.hot-topic-avatar image,
.hot-topic-avatar > image {
  width: 60rpx !important;
  height: 60rpx !important;
  display: block;
  object-fit: contain;
  margin: auto;
}

.hot-topic-username {
  margin-left: 15rpx;
  font-size: 28rpx;
  color: #333;
}

.hot-topic-content {
  display: flex;
  flex-direction: column;
  margin-top: 10rpx;
  overflow: hidden;
}

.hot-topic-list {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
  width: 100%;
  overflow: hidden;
}

.hot-topic-item-row {
  display: flex;
  align-items: flex-start;
  line-height: 45rpx;
  width: 100%;
  overflow: hidden;
}

.hot-topic-rank {
  font-size: 32rpx;
  color: #ecba16;
  font-weight: bold;
  margin-right: 10rpx;
  flex-shrink: 0;
}

/* 修改热榜序号样式 */
.hot-topic-item-row .hot-topic-rank {
  margin-left: 15rpx;
  margin-right: 15rpx;
  color: #ecba16;
  font-size: 28rpx;
  font-weight: bold;
}

.hot-topic-text {
  font-size: 28rpx;
  line-height: 45rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1;
  max-height: 45rpx;
}

/* 热门话题空状态样式 */
.hot-topic-empty-text {
  color: #999 !important;
}

/* bg0样式使用全局app.wxss中的定义 */

.close-icon {
  width: 24rpx;
  height: 24rpx;
  padding: 8rpx;
}

/* 瀑布流样式 */
.waterfall-container {
  display: flex;
  padding: 0 16rpx;
  margin-top: 10rpx;
}

.waterfall-column {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.waterfall-column:first-child {
  margin-right: 9rpx;
}

.waterfall-column:last-child {
  margin-left: 9rpx;
}

.waterfall-card {
  background: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  transition: transform 0.2s ease;
}

.waterfall-card:active {
  transform: scale(0.98);
}

.card-image-container {
  position: relative;
  width: 100%;
}

.card-image {
  width: 100%;
  border-radius: 16rpx 16rpx 0 0;
  overflow: hidden;
  position: relative;
  min-height: 200rpx;
  max-height: 33vh;
}

.waterfall-image {
  width: 100%;
  height: auto;
  max-height: none;
  display: block;
  border-radius: 16rpx 16rpx 0 0;
}

.text-image {
  width: 100%;
  height: 280rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 16rpx 16rpx 0 0;
  position: relative;
}

.text-image-content {
  font-size: 32rpx;
  font-weight: 600;
  color: #fff;
  text-align: center;
  padding: 20rpx;
  line-height: 1.4;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
  word-break: break-all;
  white-space: pre-wrap;
}

.like-badge {
  position: absolute;
  bottom: 12rpx;
  right: 12rpx;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 20rpx;
  padding: 6rpx 12rpx;
  display: flex;
  align-items: center;
  gap: 6rpx;
}

.like-icon {
  width: 24rpx;
  height: 24rpx;
}

.like-count {
  color: #fff;
  font-size: 22rpx;
  font-weight: 500;
}

/* 低价标签样式 */
.low-price-tag {
  position: absolute;
  top: 12rpx;
  right: 12rpx;
  background: #ff4757;
  border-radius: 20rpx;
  padding: 8rpx 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(255, 71, 87, 0.4);
  transform: rotate(-8deg);
  z-index: 10;
  border: 3rpx solid #fff;
  animation: pulse 2s infinite;
}

.low-price-text {
  color: #fff;
  font-size: 24rpx;
  font-weight: bold;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
  letter-spacing: 2rpx;
}

/* 脉冲动画 */
@keyframes pulse {
  0% {
    transform: rotate(-8deg) scale(1);
    box-shadow: 0 4rpx 12rpx rgba(255, 71, 87, 0.4);
  }
  50% {
    transform: rotate(-8deg) scale(1.05);
    box-shadow: 0 6rpx 16rpx rgba(255, 71, 87, 0.6);
  }
  100% {
    transform: rotate(-8deg) scale(1);
    box-shadow: 0 4rpx 12rpx rgba(255, 71, 87, 0.4);
  }
}

.card-bottom {
  padding: 16rpx;
}

.card-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  line-height: 1.4;
  margin-bottom: 12rpx;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: pre-wrap;
  word-break: break-all;
}

.card-user-info {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.user-avatar {
  width: 32rpx;
  height: 32rpx;
  border-radius: 8rpx;
}

.username {
  font-size: 24rpx;
  color: #666;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  min-width: 0;
}

.anonymous-badge {
  background: #ff6b6b;
  color: #fff;
  font-size: 20rpx;
  padding: 2rpx 8rpx;
  border-radius: 8rpx;
}

.card-price {
  font-size: 26rpx;
  color: rgb(226, 114, 23);
  font-weight: 500;
  margin-left: auto;
  flex-shrink: 0;
}

/* 热帖提示弹窗样式 */
.hot-topic-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  visibility: hidden;
  opacity: 0;
  transition: all 0.3s ease;
}

.hot-topic-modal.show {
  visibility: visible;
  opacity: 1;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
}

/* 学校选择提示样式 */
.school-prompt-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  padding: 40rpx;
  margin-top: 10rpx;
}

.school-prompt-content {
  text-align: center;
  background: white;
  border-radius: 24rpx;
  padding: 60rpx 40rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  max-width: 500rpx;
  width: 100%;
}

.school-prompt-title {
  display: block;
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
  line-height: 1.4;
}

.school-prompt-subtitle {
  display: block;
  font-size: 28rpx;
  color: #666;
  margin-bottom: 40rpx;
  line-height: 1.4;
}

.school-prompt-button {
  background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
  color: white;
  border-radius: 50rpx;
  padding: 24rpx 48rpx;
  display: inline-block;
  box-shadow: 0 4rpx 16rpx rgba(255, 107, 107, 0.3);
  transition: all 0.3s ease;
}

.school-prompt-button:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(255, 107, 107, 0.3);
}

.school-prompt-button-text {
  font-size: 32rpx;
  font-weight: 500;
}

.modal-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 600rpx;
  background: #fff;
  border-radius: 20rpx;
  overflow: hidden;
}

.modal-header {
  padding: 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1rpx solid #eee;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.modal-close {
  width: 32rpx;
  height: 32rpx;
  padding: 10rpx;
}

.modal-body {
  padding: 40rpx 30rpx;
}

.modal-text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

.modal-footer {
  padding: 20rpx 30rpx 30rpx;
  text-align: center;
}

.modal-btn {
  background: #CED5E9;
  color: #333;
  font-size: 28rpx;
  padding: 16rpx 40rpx;
  border-radius: 40rpx;
  border: none;
  display: inline-block;
}

.hot-topic-close-x {
  font-size: 36rpx;
  color: #ecba16;
  font-weight: bold;
  cursor: pointer;
  margin-left: 20rpx;
  margin-right: 10rpx;
  user-select: none;
}

.hot-topic-view-btn {
  background: #f64f59;
  color: #fff;
  border: none;
  padding: 4rpx 18rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
  margin-left: 15rpx;
  font-weight: bold;
  cursor: pointer;
}

/* cyber-badge样式已移至组件中 */

/* 热帖"点击查看"标签 - 完全复制匿名样式 */
.hot-topic-title-container {
  display: flex;
  align-items: center;
}

.hot-cyber-badge {
  position: relative;
  height: 32rpx;
  width: 100rpx;
  margin-left: 15rpx;
  display: inline-block;
}

.hot-cyber-btn {
  --primary: #ff184c;
  --shadow-primary: #fded00;
  --color: white;
  --clip: polygon(11% 0, 95% 0, 100% 25%, 90% 90%, 95% 90%, 85% 90%, 85% 100%, 7% 100%, 0 80%);
  --border: 1.5rpx;

  color: var(--color);
  text-transform: uppercase;
  font-size: 18rpx;
  letter-spacing: 0.3rpx;
  position: relative;
  font-weight: 900;
  width: 100%;
  height: 100%;
  line-height: 32rpx;
  text-align: center;
}

.hot-cyber-btn::after, .hot-cyber-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  clip-path: var(--clip);
  /* z-index: -1;  移除这个问题样式 */
}

.hot-cyber-btn::before {
  background: var(--shadow-primary);
  transform: translate(var(--border), 0);
}

.hot-cyber-btn::after {
  background: var(--primary);
}

.hot-cyber-btn__text {
  position: relative;
  z-index: 2;
}

.hot-cyber-number {
  background: var(--shadow-primary);
  color: #323232;
  font-size: 10rpx;
  font-weight: 700;
  letter-spacing: 0.1rpx;
  position: absolute;
  width: 20rpx;
  height: 8rpx;
  top: 0;
  left: 82%;
  line-height: 8rpx;
  text-align: center;
  z-index: 3;
}

/* 自定义导航栏样式 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: rgb(245, 239, 227);
  z-index: 1000; /* 导航栏基础层级 */
  border-bottom: none;
}

.navbar-content {
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.navbar-left {
  width: 32px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  margin-left: 27px; /* 原来12px + 30rpx(15px) = 27px */
}

.navbar-right {
  width: 32px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-right: 12px;
}

.menu-icon {
  width: 44rpx;
  height: 44rpx;
}

.navbar-center {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.navbar-title {
  font-family: '北航-刀隶体 Regular';
  font-size: 62rpx;
  font-weight: bold;
  color: #333;
}

.placeholder {
  width: 44rpx;
  height: 44rpx;
  opacity: 0;
}

.navbar-placeholder {
  width: 100%;
}

/* 侧边菜单样式 */
.side-menu-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 999999 !important; /* 使用!important和更高的z-index */
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.side-menu-mask.show {
  opacity: 1;
  visibility: visible;
}

.side-menu {
  position: absolute;
  top: 0;
  left: 0;
  width: 560rpx;
  height: 100vh;
  background: white;
  transform: translateX(-100%);
  transition: transform 0.3s ease;
  display: flex;
  flex-direction: column;
  z-index: 999999 !important; /* 确保侧边栏也有最高层级 */
}

.side-menu.slide-in {
  transform: translateX(0);
}

.side-menu.slide-out {
  transform: translateX(-100%);
}

.side-menu-header {
  padding-top: calc(var(--status-bar-height) + 44px + 30rpx); /* 状态栏高度 + navbar高度 + 额外间距 */
  padding-bottom: 40rpx;
  padding-left: 40rpx;
  padding-right: 40rpx;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%); /* 更柔和的灰蓝色渐变 */
  color: #333; /* 深色文字 */
}

.user-info {
  display: flex;
  align-items: center;
  position: relative;
}

.user-info.clickable {
  cursor: pointer;
}

.user-info.clickable:active {
  opacity: 0.7;
}

.side-menu .user-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  margin-right: 30rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.8); /* 更明显的白色边框 */
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1); /* 添加阴影 */
}

.user-details {
  display: flex;
  flex-direction: column;
}

.user-name {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.user-school {
  font-size: 28rpx;
  opacity: 0.8;
}

.select-school-arrow {
  width: 32rpx;
  height: 32rpx;
  margin-left: auto;
  opacity: 0.6;
}

.side-menu-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 40rpx 0 0 0;
  overflow-y: auto; /* 添加垂直滚动 */
  max-height: calc(100vh - 300rpx); /* 限制最大高度，为底部菜单留出空间 */
}

.main-menu-section {
  flex: 1;
  overflow-y: auto; /* 主菜单区域可滚动 */
  padding-bottom: 20rpx; /* 底部留出间距 */
}

.bottom-menu-section {
  padding: 40rpx 0 60rpx 0;
  border-top: 1rpx solid #f0f0f0;
  margin-top: 40rpx;
  display: flex;
  justify-content: space-around;
  align-items: center;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 30rpx 40rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.menu-item:active {
  background: #f8f8f8;
}

.menu-item .menu-icon {
  width: 48rpx;
  height: 48rpx;
  margin-right: 30rpx;
}

.menu-text {
  flex: 1;
  font-size: 32rpx;
  color: #333;
}

.arrow-icon {
  width: 32rpx;
  height: 32rpx;
  opacity: 0.5;
  transition: transform 0.3s ease;
}

.arrow-icon.rotated {
  transform: rotate(90deg);
}

/* 万能查询子菜单样式 */
.submenu-container {
  overflow: hidden;
  transition: max-height 0.3s ease, opacity 0.3s ease;
  margin: 0 40rpx 0 40rpx;
}

.submenu-container.collapsed {
  max-height: 0;
  opacity: 0;
}

.submenu-container.expanded {
  max-height: 500rpx; /* 增加最大高度以容纳更多子菜单项 */
  opacity: 1;
}

.submenu-item {
  display: flex;
  align-items: center;
  padding: 20rpx 70rpx;
  margin-bottom: 10rpx;
  border-bottom: 1rpx solid #e8e8e8;
}

.submenu-item:last-child {
  border-bottom: none;
}

.submenu-item:active {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 8rpx;
}

.submenu-icon {
  width: 36rpx;
  height: 36rpx;
  margin-right: 24rpx;
  opacity: 0.7;
}

.submenu-text {
  font-size: 28rpx;
  color: #666;
}

/* 底部菜单项样式 */
.bottom-menu-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20rpx;
  min-width: 120rpx;
}

.bottom-menu-item:active {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 20rpx;
}

.bottom-menu-icon {
  width: 48rpx;
  height: 48rpx;
  margin-bottom: 16rpx;
  opacity: 0.7;
}

.bottom-menu-text {
  font-size: 24rpx;
  color: #666;
  text-align: center;
}
