<!-- 设置背景 -->
<view class="gradient-background">
  <!-- 自定义导航栏 -->
  <view class="custom-navbar" style="padding-top: {{statusBarHeight}}px;">
    <view class="navbar-content">
      <view class="navbar-left" bindtap="toggleSideMenu">
        <image src="/images/caidan.png" class="menu-icon"></image>
      </view>
      <view class="navbar-center">
        <text class="navbar-title">{{navSchoolText}}</text>
      </view>
      <view class="navbar-right">
        <view class="placeholder"></view>
      </view>
    </view>
  </view>
  <!-- 导航栏占位 -->
  <view class="navbar-placeholder" style="height: {{statusBarHeight + 44}}px;"></view>
  <!-- 顶部标签 -->
  <view class="grid-list {{guideStep === 5 && showGuideHint && !guideClosing ? 'guide-active' : ''}}" style="top: {{gridListTop}}px;">
    <!-- 标签页区域 -->
    <view class="grid-tabs">
      <view class="grid-item " wx:for="{{grid}}" wx:key="number" bindtap="selectItem" data-number="{{item.number}}">
        <image src="{{item.icon}}" mode="widthFix"></image>
        <text wx:if="{{selectedItemId === item.number}}" style="font-weight: bold;color: rgb(231, 112, 0);">{{item.text}}</text>
        <text wx:else style="color: black">{{item.text}}</text>
      </view>
      <!-- 滑动横条指示器 -->
      <view class="indicator-bar" style="transform: translateX({{indicatorPosition}}rpx);"></view>
    </view>

    <!-- 定位和筛选按钮区域 -->
    <view class="filter-buttons-row">
      <view class="filter-button" bindtap="onLocationTap">
        <image src="/images/sijiaoxing.png" class="filter-icon"></image>
        <text class="filter-text">{{currentSchoolText}}</text>
      </view>
      <view class="filter-button filter-select" bindtap="onFilterTap">
        <text class="filter-text">{{selectedItemId === 1 ? currentFilterText1 : selectedItemId === 2 ? currentFilterText2 : selectedItemId === 3 ? currentFilterText3 : currentFilterText4}}</text>
        <image src="/images/xiangxiajiantou.png" class="filter-arrow"></image>
      </view>
    </view>
  </view>


  <!-- 不同页面内容 - 支持滑动切换 -->
  <swiper class="content-swiper" style="top: {{swiperTop}}px; height: {{swiperHeight}}px;" current="{{swiperIndex}}" bindchange="onSwiperChange" bindtransition="onSwiperTransition" bindanimationfinish="onSwiperAnimationFinish" duration="300" vertical="{{false}}" disable-touch="{{false}}">
    <swiper-item>
      <scroll-view class="swiper-content" scroll-y="true" lower-threshold="200" bindscrolltolower="onScrollToLower" data-tab="1" refresher-enabled="{{!swiperTransitioning}}" bindrefresherrefresh="onRefresherRefresh" refresher-triggered="{{refresherTriggered && selectedItemId === 1}}" data-tab-refresh="1" bindtouchstart="onTouchStart" bindtouchmove="onTouchMove" bindtouchend="onTouchEnd" style="-webkit-overflow-scrolling: touch;">
      <loading show="{{isLoading && selectedItemId === 1}}" mask="false"></loading>

      <!-- 学校选择提示 -->
      <view wx:if="{{showSchoolPrompt}}" class="school-prompt-container">
        <view class="school-prompt-content">
          <text class="school-prompt-title">欢迎来到北京学生社区</text>
          <text class="school-prompt-subtitle">请先选择您的学校</text>
          <view class="school-prompt-button" bindtap="goToSchoolSelect">
            <text class="school-prompt-button-text">点击选择</text>
          </view>
        </view>
      </view>

      <view wx:elif="{{!isLoading || selectedItemId !== 1}}">
        <!-- 添加每日热帖区域 -->
        <view wx:if="{{showHotTopic}}">
          <view class="hot-topic-container" bindtap="goToHot">
            <view class="hot-topic-item">
              <view class="touxiang1" style="display: flex; justify-content: space-between; align-items: center;">
                <view style="display: flex; align-items: center;">
                  <view class="hot-topic-avatar">
                    <image src="/images/remen.png" style="width:60rpx;height:60rpx;display:block;margin:auto;" mode="aspectFit" />
                  </view>
                  <view class="hot-topic-title-container">
                    <text style="margin-left:15rpx; font-weight: bold;">每日热帖</text>
                    <view class="hot-cyber-badge">
                      <view class="hot-cyber-btn">
                        <view class="hot-cyber-btn__text">点击查看</view>
                        <view class="hot-cyber-number">GO</view>
                      </view>
                    </view>
                  </view>
                </view>
                <image src="/images/guanbi.png" class="close-icon" catchtap="showHotTopicModal"></image>
              </view>
              <view class="hot-topic-content">
                <view class="hot-topic-list">
                  <!-- 有热门内容时显示 -->
                  <block wx:if="{{displayTopics && displayTopics.length > 0}}">
                    <view class="hot-topic-item-row" wx:for="{{displayTopics}}" wx:key="index">
                      <text class="hot-topic-rank">{{item.rank}}.</text>
                      <text class="hot-topic-text">{{item.content}}</text>
                    </view>
                  </block>
                  <!-- 没有热门内容时显示填充内容 -->
                  <view wx:else class="hot-topic-item-row">
                    <text class="hot-topic-rank">1.</text>
                    <text class="hot-topic-text hot-topic-empty-text">暂时没有热门消息哦</text>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>
        <!-- 原有的消息列表 -->
        <message-item
          wx:for="{{messages1}}"
          wx:key="index"
          item="{{item}}"
          index="{{index}}"
          abc="{{abc}}"
          backgroundColor="rgba({{abc[0]}}, {{abc[1]}}, {{abc[2]}}, 0.8)"
          bind:messagedetail="viewMessageDetail"
          bind:likechange="onLikeChange"
        />
        <view style="height: 30rpx;"></view>
      </view>
    </scroll-view>
    </swiper-item>



    <swiper-item>
      <scroll-view class="swiper-content" scroll-y="true" lower-threshold="200" bindscrolltolower="onScrollToLower" data-tab="2" refresher-enabled="{{!swiperTransitioning}}" bindrefresherrefresh="onRefresherRefresh" refresher-triggered="{{refresherTriggered && selectedItemId === 2}}" data-tab-refresh="2" style="-webkit-overflow-scrolling: touch;">
      <loading show="{{isLoading && selectedItemId === 2}}" mask="false"></loading>

      <!-- 学校选择提示 -->
      <view wx:if="{{showSchoolPrompt}}" class="school-prompt-container">
        <view class="school-prompt-content">
          <text class="school-prompt-title">欢迎来到北京学生社区</text>
          <text class="school-prompt-subtitle">请先选择您的学校</text>
          <view class="school-prompt-button" bindtap="goToSchoolSelect">
            <text class="school-prompt-button-text">点击选择</text>
          </view>
        </view>
      </view>

      <view wx:elif="{{!isLoading || selectedItemId !== 2}}">
    <!-- 校园交易群提示 -->
    <group-notice choose="3" />
    <!-- 校园交易板块 - 小红书风格瀑布流 -->
    <view class="waterfall-container">
      <view class="waterfall-column">
        <view wx:for="{{leftColumnMessages2}}" wx:key="index" class="waterfall-card" bindtap="viewMessageDetail" data-index="{{index}}" data-column="left">
          <!-- 图片或文字图片 -->
          <view class="card-image-container">
            <view wx:if="{{item.images && item.images.length > 0}}" class="card-image">
              <image src="{{item.images[0]}}" mode="widthFix" class="waterfall-image"></image>
            </view>
            <view wx:else class="text-image" style="background: {{item.textImageBg}};">
              <text class="text-image-content">{{item.textImageText}}</text>
            </view>
            <!-- 右上角低价标签 -->
            <view wx:if="{{item.flag === 2}}" class="low-price-tag">
              <text class="low-price-text">低价</text>
            </view>
            <!-- 右下角位置信息 -->
            <view wx:if="{{item.displayWeizhi && item.displayWeizhi !== '未知'}}" class="like-badge">
              <text class="like-count">{{item.displayWeizhi}}</text>
            </view>
          </view>

          <!-- 卡片底部信息 -->
          <view class="card-bottom">
            <view class="card-title">{{item.displayTitle}}</view>
            <view class="card-user-info">
              <image src="{{item.face_url}}" class="user-avatar"></image>
              <text class="username">{{item.username}}</text>
              <view wx:if="{{item.is_anonymous}}" class="anonymous-badge">匿名</view>
              <view class="card-price">{{item.jine || '无'}}</view>
            </view>
          </view>
        </view>
      </view>

      <view class="waterfall-column">
        <view wx:for="{{rightColumnMessages2}}" wx:key="index" class="waterfall-card" bindtap="viewMessageDetail" data-index="{{index}}" data-column="right">
          <!-- 图片或文字图片 -->
          <view class="card-image-container">
            <view wx:if="{{item.images && item.images.length > 0}}" class="card-image">
              <image src="{{item.images[0]}}" mode="widthFix" class="waterfall-image"></image>
            </view>
            <view wx:else class="text-image" style="background: {{item.textImageBg}};">
              <text class="text-image-content">{{item.textImageText}}</text>
            </view>
            <!-- 右上角低价标签 -->
            <view wx:if="{{item.flag === 2}}" class="low-price-tag">
              <text class="low-price-text">低价</text>
            </view>
            <!-- 右下角位置信息 -->
            <view wx:if="{{item.displayWeizhi && item.displayWeizhi !== '未知'}}" class="like-badge">
              <text class="like-count">{{item.displayWeizhi}}</text>
            </view>
          </view>

          <!-- 卡片底部信息 -->
          <view class="card-bottom">
            <view class="card-title">{{item.displayTitle}}</view>
            <view class="card-user-info">
              <image src="{{item.face_url}}" class="user-avatar"></image>
              <text class="username">{{item.username}}</text>
              <view wx:if="{{item.is_anonymous}}" class="anonymous-badge">匿名</view>
              <view class="card-price">{{item.jine || '无'}}</view>
            </view>
          </view>
        </view>
      </view>
      <view style="height: 30rpx;"></view>
    </view>
      </view>
      </scroll-view>
    </swiper-item>

    <swiper-item>
      <scroll-view class="swiper-content" scroll-y="true" style="-webkit-overflow-scrolling: touch;">
        <!-- 学校选择提示 -->
        <view wx:if="{{showSchoolPrompt}}" class="school-prompt-container">
          <view class="school-prompt-content">
            <text class="school-prompt-title">欢迎来到北京学生社区</text>
            <text class="school-prompt-subtitle">请先选择您的学校</text>
            <view class="school-prompt-button" bindtap="goToSchoolSelect">
              <text class="school-prompt-button-text">点击选择</text>
            </view>
          </view>
        </view>

        <!-- 大学城树洞预告页面 -->
        <view wx:else class="preview-container">
          <view class="preview-content">
            <text class="preview-title">更新预告</text>
            <text class="preview-subtitle">下学期上线</text>
          </view>
        </view>
      </scroll-view>
    </swiper-item>

    <swiper-item>
      <scroll-view class="swiper-content" scroll-y="true" style="-webkit-overflow-scrolling: touch;">
        <!-- 学校选择提示 -->
        <view wx:if="{{showSchoolPrompt}}" class="school-prompt-container">
          <view class="school-prompt-content">
            <text class="school-prompt-title">欢迎来到北京学生社区</text>
            <text class="school-prompt-subtitle">请先选择您的学校</text>
            <view class="school-prompt-button" bindtap="goToSchoolSelect">
              <text class="school-prompt-button-text">点击选择</text>
            </view>
          </view>
        </view>

        <!-- 跑腿代办预告页面 -->
        <view wx:else class="preview-container">
          <view class="preview-content">
            <text class="preview-title">更新预告</text>
            <text class="preview-subtitle">下学期上线</text>
          </view>
        </view>
      </scroll-view>
    </swiper-item>
  </swiper>

  <!-- 回顶部按钮 -->
  <view class="fixed-button" bindtap="scrollToTop">
    <image class="custom-icon" src="/images/up-arrow.png"></image>
  </view>
  <view class="fixed-button2 {{guideStep === 2 ? 'guide-active' : ''}}" bindtap="publish">
    <image class="custom-icon" src="/images/addyeah.jpeg"></image>
  </view>
  <view class="fixed-button3 {{guideStep === 3 ? 'guide-active' : ''}}" bindtap="vxgroup">
    <image class="custom-icon" src="/images/weixin-01.png"></image>
  </view>
  <!-- 添加热门按钮 -->
  <view class="fixed-button5" bindtap="goToHot">
    <image class="custom-icon" src="/images/remen.png"></image>
  </view>
  <view class="fixed-button4 {{guideStep === 4 ? 'guide-active' : ''}}" bindtap="showGuide">
    <image class="custom-icon" src="/images/wenhao.png"></image>
  </view>
</view>

<!-- 更新公告 -->
<view class="update-notice-mask {{showUpdateNotice ? 'update-show' : 'update-hide'}} transform-gpu" wx:if="{{showUpdateNotice || noticeClosing}}" catchtouchmove="stopPropagation">
  <view class="update-mask-background transform-gpu" bindtap="{{showUpdateNotice ? 'closeUpdateNotice' : 'stopPropagation'}}"></view>
  <view animation="{{updateNoticeAnimation}}" class="update-notice-box {{showUpdateNotice ? 'notice-show' : ''}}" catchtap="stopPropagation">
    <view class="update-notice-header">
      <image src="/images/xiaoyuan.png" mode="aspectFit" class="update-notice-icon"></image>
      <text class="update-title">大版本更新公告</text>
      <text class="update-version">Version 5.0.0</text>
      <text class="update-date">2025年6月23日</text>
    </view>
    <view class="update-notice-body">
      <text class="update-text" decode="{{true}}" space="emsp">1.全新改版了校园交易板块，新增了低价急出二手交易群，适用于低价抛售的商品，大家可以点击 首页右侧的微信图标 进群捡漏hhhh
2.全新改版了切换分类的方式，可以滑动切换分类，为下学期大学城树洞做准备和测试啦</text>
    </view>
    <view class="update-notice-footer">
      <view class="update-btn-group">
        <!-- <button class="update-btn update-btn-later" bindtap="closeUpdateNotice">我知道了</button> -->
        <button class="update-btn update-btn-check" bindtap="closeUpdateNotice"></button>
        <!-- <button class="update-btn update-btn-check" bindtap="goToGongju">去看看</button> -->
      </view>
    </view>
  </view>
</view>

<!-- 引导提示遮罩层 -->
<view class="guide-hint-mask {{showGuideHint ? 'guide-show' : 'guide-hide'}} {{guideStep === 5 ? 'step-5' : ''}}" wx:if="{{showGuideHint || guideClosing}}">
  <view class="mask-background" catchtap="stopPropagation"></view>
  <!-- 第一步：欢迎页 -->
  <view wx:if="{{guideStep === 1}}" class="guide-hint-content" catchtap="stopPropagation">
    <view class="skip-guide" catchtap="skipGuide">跳过引导</view>
    <view class="guide-hint-header">
      <image src="/images/xiaoyuan.png" mode="aspectFit" class="guide-hint-icon"></image>
      <text class="guide-hint-title">灵行BUAA</text>
    </view>
    <view class="guide-hint-body">
      <text class="guide-text">uu好呀，灵行BUAA欢迎你，我是由23级在校生开发的非盈利树洞小程序，无广告/钓鱼，构建和谐健康的校园社区生态.</text>
    </view>
    <view class="guide-hint-footer">
      <button class="guide-hint-btn gradient-btn pulse" bindtap="nextGuideStep">开始探索 ↓</button>
    </view>
  </view>

  <!-- 第二步：发布按钮 -->
  <view wx:if="{{guideStep === 2}}" class="highlight-publish" catchtap="stopPropagation">
    <view class="skip-guide" catchtap="skipGuide">跳过引导</view>
    <view class="guide-hint-body">
      <text class="guide-text">✏️ 点击这里可以发布帖子，不过需要先去个人中心完成学生认证，非本校学生不能发布</text>
    </view>
    <view class="guide-hint-footer">
      <button class="guide-hint-btn" bindtap="nextGuideStep">下一步</button>
    </view>
  </view>

  <!-- 第三步：微信群功能 -->
  <view wx:if="{{guideStep === 3}}" class="highlight-nav" catchtap="stopPropagation">
    <view class="skip-guide" catchtap="skipGuide">跳过引导</view>
    <view class="guide-hint-body">
      <text class="guide-text">💬 点击这里可以加入树洞消息推送群，有需要的同学可以加入，开发者也在群里，有问题可以直接问或者加微信</text>
    </view>
    <view class="guide-hint-footer">
      <button class="guide-hint-btn" bindtap="nextGuideStep">下一步</button>
    </view>
  </view>

  <!-- 第四步：问号按钮说明 -->
  <view wx:if="{{guideStep === 4}}" class="highlight-content" catchtap="stopPropagation">
    <view class="skip-guide" catchtap="skipGuide">跳过引导</view>
    <view class="guide-hint-body">
      <text class="guide-text">❓ 点击这个可以查看说明，每个页面的说明都不一样哦！</text>
    </view>
    <view class="guide-hint-footer">
      <button class="guide-hint-btn" bindtap="nextGuideStep">下一步</button>
    </view>
  </view>

  <!-- 第五步：消息详情 -->
  <view wx:if="{{guideStep === 5}}" class="highlight-message" catchtap="stopPropagation">
    <view class="skip-guide" catchtap="skipGuide">跳过引导</view>
    <view class="guide-hint-body">
      <text class="guide-text">🔍 点击这里可以切换分类，快来发现更多有趣内容吧</text>
    </view>
    <view class="guide-hint-footer">
      <button class="guide-hint-btn" bindtap="closeGuideHint">开始体验</button>
    </view>
  </view>
</view>

<!-- 在页面底部添加发布弹窗组件 -->
<publish-popup visible="{{showPublishPopup}}" bind:close="closePublishPopup"></publish-popup>

<view wx:if="{{showPrivacy}}">
  <view>隐私弹窗内容....</view>
  <button bindtap="handleOpenPrivacyContract">查看隐私协议</button>
  <button id="agree-btn" open-type="agreePrivacyAuthorization" bindagreeprivacyauthorization="handleAgreePrivacyAuthorization">同意</button>
</view>

<!-- 热帖提示弹窗 -->
<view class="hot-topic-modal {{showHotTopicModal ? 'show' : ''}}" catchtouchmove="stopPropagation">
  <view class="modal-mask" bindtap="closeHotTopicModal"></view>
  <view class="modal-content">
    <view class="modal-header">
      <text class="modal-title">提示</text>
      <image src="/images/guanbi.png" class="modal-close" bindtap="closeHotTopicModal"></image>
    </view>
    <view class="modal-body">
      <text class="modal-text">如果您想关闭每日热榜，可以去右下角个人中心中的设置里关闭</text>
    </view>
    <view class="modal-footer">
      <button class="modal-btn" bindtap="closeHotTopicModal">我知道了</button>
    </view>
  </view>
</view>

<!-- 筛选弹窗组件 -->
<filter-popup
  show="{{showFilterPopup}}"
  title="选择消息类型"
  options="{{filterOptions}}"
  selectedValue="{{currentFilter}}"
  bind:select="onFilterSelect"
  bind:close="onFilterClose"
></filter-popup>

<!-- 侧边菜单 -->
<view class="side-menu-mask {{showSideMenu ? 'show' : ''}}" bindtap="closeSideMenu" catchtouchmove="stopPropagation">
  <view class="side-menu {{showSideMenu ? 'slide-in' : 'slide-out'}}"
        catchtap="stopPropagation"
        bindtouchstart="onSideMenuTouchStart"
        bindtouchmove="onSideMenuTouchMove"
        bindtouchend="onSideMenuTouchEnd">
    <view class="side-menu-header" style="--status-bar-height: {{statusBarHeight}}px;">
      <view class="user-info {{userInfo.needSelectSchool ? 'clickable' : ''}}" bindtap="{{userInfo.needSelectSchool ? 'handleUserInfoClick' : ''}}">
        <image src="{{userInfo.avatar}}" class="user-avatar"></image>
        <view class="user-details">
          <text class="user-name">{{userInfo.username || '北航学子'}}</text>
          <text class="user-school">{{userInfo.school || '北京航空航天大学'}}</text>
        </view>
        <!-- 当需要选择学校时显示箭头 -->
        <image wx:if="{{userInfo.needSelectSchool}}" src="/images/youjiantou-3.png" class="select-school-arrow"></image>
      </view>
    </view>

    <view class="side-menu-content">
      <!-- 主要功能区域 -->
      <view class="main-menu-section">
        <view class="menu-item" bindtap="goToProfile">
          <image src="/images/gerenziliao.png" class="menu-icon"></image>
          <text class="menu-text">个人资料</text>
          <image src="/images/youjiantou-3.png" class="arrow-icon"></image>
        </view>

        <!-- 万能查询 -->
        <view class="menu-item expandable-menu" bindtap="toggleUniversalQuery">
          <image src="/images/fangdajing.png" class="menu-icon"></image>
          <text class="menu-text">万能查询</text>
          <image src="/images/youjiantou-3.png" class="arrow-icon {{showUniversalQuery ? 'rotated' : ''}}"></image>
        </view>

        <!-- 万能查询子菜单 -->
        <view class="submenu-container {{showUniversalQuery ? 'expanded' : 'collapsed'}}">
          <view class="submenu-item" bindtap="goToGradeQuery">
            <image src="{{gradeQueryIcon}}" class="submenu-icon"></image>
            <text class="submenu-text">成绩查询</text>
          </view>

          <view class="submenu-item" bindtap="goToSchoolBus">
            <image src="/images/xiaoche-2.png" class="submenu-icon"></image>
            <text class="submenu-text">校车时刻表</text>
          </view>

          <view class="submenu-item" bindtap="goToSchoolCalendar">
            <image src="/images/kebiao.png" class="submenu-icon"></image>
            <text class="submenu-text">校历</text>
          </view>

          <view class="submenu-item" bindtap="goToDepartmentTable">
            <image src="/images/gongneng.png" class="submenu-icon"></image>
            <text class="submenu-text">系号表</text>
          </view>
        </view>

        <view class="menu-item" bindtap="goToSchoolSelect">
          <image src="/images/sijiaoxing.png" class="menu-icon"></image>
          <text class="menu-text">切换学校</text>
          <image src="/images/youjiantou-3.png" class="arrow-icon"></image>
        </view>
      </view>

      <!-- 底部功能区域 -->
      <view class="bottom-menu-section">
        <view class="bottom-menu-item" bindtap="goToUpdateNotice">
          <image src="/images/yewushouce.png" class="bottom-menu-icon"></image>
          <text class="bottom-menu-text">更新公告</text>
        </view>

        <view class="bottom-menu-item" bindtap="goToAbout">
          <image src="/images/weixiao.png" class="bottom-menu-icon"></image>
          <text class="bottom-menu-text">关于我们</text>
        </view>

        <view class="bottom-menu-item" bindtap="goToSettings">
          <image src="/images/shezhi.png" class="bottom-menu-icon"></image>
          <text class="bottom-menu-text">系统设置</text>
        </view>
      </view>
    </view>
  </view>
</view>