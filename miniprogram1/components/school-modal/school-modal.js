// 自定义学校弹窗组件
Component({
  properties: {
    show: {
      type: Boolean,
      value: false
    },
    schoolName: {
      type: String,
      value: ''
    }
  },

  methods: {
    // 关闭弹窗
    onClose() {
      this.triggerEvent('close');
    },

    // 点击遮罩关闭
    onMaskTap() {
      this.onClose();
    },

    // 阻止事件冒泡
    stopPropagation() {
      // 阻止点击弹窗内容时关闭弹窗
    },

    // 复制微信号
    onCopy() {
      wx.setClipboardData({
        data: 'Wenroujun1',
        success: () => {
          wx.showToast({
            title: '已复制到剪贴板',
            icon: 'success',
            duration: 2000
          });
        },
        fail: () => {
          wx.showToast({
            title: '复制失败',
            icon: 'none',
            duration: 2000
          });
        }
      });
    }
  }
});
