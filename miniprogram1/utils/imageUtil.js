/**
 * 图片URL处理工具 - 支持COS相对路径
 */

/**
 * 根据当前环境获取COS域名
 */
function getCurrentCosDomain(bucketType = 'public') {
  const app = getApp();
  const baseUrl = app.globalData.wangz;

  if (baseUrl.includes('localhost')) {
    // 本地环境使用默认COS域名
    if (bucketType === 'private') {
      return 'https://treehole-1320255796.cos.ap-beijing.myqcloud.com';
    } else {
      return 'https://treeholepublic-1320255796.cos.ap-beijing.myqcloud.com';
    }
  } else {
    // 生产环境使用自定义CDN域名
    if (bucketType === 'private') {
      return 'https://private.bjgaoxiaoshequ.store';
    } else {
      return 'https://public.bjgaoxiaoshequ.store';
    }
  }
}

/**
 * 处理图片URL，支持COS相对路径自动拼接域名
 * @param {string} imageUrl - 原始图片URL
 * @param {string} defaultUrl - 默认图片URL
 * @param {string} bucketType - 存储桶类型
 * @returns {string} 处理后的完整URL
 */
function processImageUrl(imageUrl, defaultUrl = null, bucketType = 'public') {
  if (!imageUrl) {
    return defaultUrl
  }

  // 如果已经是完整URL（包含http），直接返回
  if (imageUrl.startsWith('http')) {
    return imageUrl
  }

  // 如果是COS对象键（相对路径，包含斜杠），拼接COS域名
  if (imageUrl.includes('/') && !imageUrl.startsWith('/uploads/')) {
    // 自动判断存储桶类型
    const detectedBucketType = getBucketTypeFromPath(imageUrl);
    const domain = getCurrentCosDomain(detectedBucketType);
    const fullUrl = domain + '/' + imageUrl;
    return fullUrl;
  }

  // 如果是旧的本地上传图片（以/uploads/开头），添加域名前缀
  if (imageUrl.startsWith('/uploads/')) {
    const fullUrl = getApp().globalData.wangz + imageUrl;
    return fullUrl;
  }

  // 其他情况（如本地图片路径），直接返回
  return imageUrl
}

/**
 * 根据图片路径自动判断存储桶类型
 * @param {string} imagePath - 图片路径
 * @returns {string} 存储桶类型 'public' 或 'private'
 */
function getBucketTypeFromPath(imagePath) {
  // 公有存储桶的文件类型
  const publicTypes = ['comment', 'life', 'group', 'canteen', 'activity', 'liaoran', 'common', 'avatar', 'anonymous-avatar'];

  // 检查路径中是否包含公有类型（支持环境前缀）
  for (const type of publicTypes) {
    // 支持 dev/comment/ 或 prod/comment/ 格式
    if (imagePath.includes(`/${type}/`) ||
        imagePath.startsWith(`${type}/`) ||
        imagePath.includes(`/dev/${type}/`) ||
        imagePath.includes(`/prod/${type}/`) ||
        imagePath.startsWith(`dev/${type}/`) ||
        imagePath.startsWith(`prod/${type}/`)) {
      return 'public';
    }
  }

  // 默认为私有存储桶
  return 'private';
}

/**
 * 批量处理图片URL
 * @param {Array} items - 包含图片URL的对象数组
 * @param {string} imageField - 图片字段名，默认为'img'
 * @param {string} defaultUrl - 默认图片URL
 * @param {string} bucketType - 存储桶类型
 * @returns {Array} 处理后的数组
 */
function processImageUrls(items, imageField = 'img', defaultUrl = null, bucketType = 'public') {
  if (!Array.isArray(items)) {
    return items
  }

  return items.map(item => {
    if (typeof item === 'object' && item[imageField]) {
      return {
        ...item,
        [imageField]: processImageUrl(item[imageField], defaultUrl, bucketType)
      }
    }
    return item
  })
}

/**
 * 处理图片数组（JSON字符串或数组）
 * @param {string|Array} images - 图片数组或JSON字符串
 * @param {string} bucketType - 存储桶类型
 * @returns {Array} 处理后的图片URL数组
 */
function processImageArray(images, bucketType = 'public') {
  if (!images) {
    return []
  }

  let imageArray = images
  if (typeof images === 'string') {
    try {
      imageArray = JSON.parse(images)
    } catch (e) {
      // 如果JSON解析失败，尝试按逗号分割
      imageArray = images.split(',').map(img => img.trim()).filter(img => img)
    }
  }

  if (!Array.isArray(imageArray)) {
    return []
  }

  return imageArray.map(img => processImageUrl(img, '', bucketType));
}

/**
 * 处理图片字符串（逗号分隔的多个图片路径）
 * @param {string} imageString - 图片路径字符串，用逗号分隔
 * @param {string} bucketType - 存储桶类型
 * @returns {Array} 处理后的图片URL数组
 */
function processImageString(imageString, bucketType = 'public') {
  if (!imageString || typeof imageString !== 'string') {
    return []
  }

  // 分割字符串并处理每个路径
  const imagePaths = imageString.split(',').map(path => path.trim()).filter(path => path)
  return imagePaths.map(path => processImageUrl(path, '', bucketType))
}

/**
 * 处理用户头像URL
 * @param {string} avatarUrl - 原始头像URL
 * @returns {string} 处理后的完整URL
 */
function processAvatarUrl(avatarUrl) {
  // 如果没有头像，直接返回本地默认头像
  if (!avatarUrl) {
    return '/images/weixiao.png';
  }

  // 如果已经是完整URL，直接返回
  if (avatarUrl.startsWith('http://') || avatarUrl.startsWith('https://')) {
    return avatarUrl;
  }

  // 如果是本地路径，直接返回
  if (avatarUrl.startsWith('/images/')) {
    return avatarUrl;
  }

  // 其他情况，处理为COS URL
  return processImageUrl(avatarUrl, '/images/weixiao.png', 'public');
}

/**
 * 统一处理消息对象中的所有图片URL
 * @param {Object} item - 消息对象
 * @returns {Object} 处理后的消息对象
 */
function processMessageImages(item) {
  if (!item || typeof item !== 'object') {
    return item;
  }

  const processedItem = { ...item };

  // 处理头像
  if (processedItem.face_url) {
    processedItem.face_url = processAvatarUrl(processedItem.face_url);
  }

  // 处理消息图片数组
  if (processedItem.images) {
    processedItem.images = processImageArray(processedItem.images);
  }

  // 处理其他可能的图片字段
  if (processedItem.cover_image) {
    processedItem.cover_image = processImageUrl(processedItem.cover_image);
  }



  return processedItem;
}

/**
 * 批量处理消息数组中的图片URL
 * @param {Array} messages - 消息数组
 * @returns {Array} 处理后的消息数组
 */
function processMessagesImages(messages) {
  if (!Array.isArray(messages)) {
    return messages;
  }

  return messages.map(item => processMessageImages(item));
}

/**
 * 根据文件类型判断存储桶类型
 */
function getBucketTypeByFileType(fileType) {
  // anonymous-avatar 特殊处理为公有桶
  if (fileType === 'anonymous-avatar') {
    return 'public';
  }

  const privateTypes = ['auth', 'schedule', 'touxiang', 'renzheng', 'kebiao']; // 移除avatar
  return privateTypes.includes(fileType) ? 'private' : 'public';
}

/**
 * 根据文件路径判断存储桶类型
 */
function getBucketTypeByPath(imagePath) {
  if (!imagePath) return 'public';

  // 特殊处理：anonymous-avatar路径始终为公有桶
  if (imagePath.includes('anonymous-avatar')) {
    return 'public';
  }

  // 检查路径中是否包含私有文件类型关键词
  const privateKeywords = ['touxiang', 'renzheng', 'kebiao', 'auth', 'schedule']; // 移除avatar
  for (let keyword of privateKeywords) {
    if (imagePath.includes(keyword)) {
      return 'private';
    }
  }

  return 'public';
}

module.exports = {
  getCurrentCosDomain,
  processImageUrl,
  processImageUrls,
  processImageArray,
  processImageString,
  processAvatarUrl,
  processMessageImages,
  processMessagesImages,
  getBucketTypeByFileType,
  getBucketTypeByPath,
  getBucketTypeFromPath
}
